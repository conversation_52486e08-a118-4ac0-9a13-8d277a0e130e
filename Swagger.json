{"openapi": "3.0.1", "info": {"title": "Transcelerate SDR", "version": "v4"}, "paths": {"/studydefinitions/{studyId}/changeaudit": {"get": {"tags": ["ChangeAudit"], "summary": "GET Change Audit For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Change Audit", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Common.ChangeAudit"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "403": {"description": "The Access for Study is Forbidden"}}}}, "/studydefinitions/{studyId}/rawdata": {"get": {"tags": ["Common"], "summary": "GET All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "200": {"description": "Returns Study"}}}}, "/versions": {"get": {"tags": ["Common"], "summary": "GET API -> USDM Version Mapping", "responses": {"200": {"description": "API -> USDM Version Mapping", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Utilities.Common.ApiUsdmVersionMapping"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/studydefinitions/{studyId}/revisionhistory": {"get": {"tags": ["Common"], "summary": "GET Revision History of a study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "fromDate", "in": "query", "description": "Start Date for Date Filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "description": "End Date for Date Filter", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "Returns a list of Audit Trail of a study", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Common.AuditTrailResponse"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Audit trail for the study is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/studydefinitions/history": {"get": {"tags": ["Common"], "summary": "Get All StudyId's in the database", "parameters": [{"name": "fromDate", "in": "query", "description": "Start Date for Date Filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "description": "End Date for Date Filter", "schema": {"type": "string", "format": "date-time"}}, {"name": "studyTitle", "in": "query", "description": "Study Title Filter", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns All Study Id's", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Common.StudyHistoryResponse"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There is no study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/studydefinitions/{studyId}/links": {"get": {"tags": ["Common"], "summary": "GET Links", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "200": {"description": "Returns Study"}}}}, "/studydefinitions/search": {"post": {"tags": ["Common"], "summary": "Search For a Study", "requestBody": {"description": "Parameters to search in database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Common.SearchParameters"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Common.SearchParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Common.SearchParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Common.SearchParameters"}}}}, "responses": {"200": {"description": "Returns All Study that matches the search criteria", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Common.SearchResponse"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There is no study that matches the search criteria", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/studydefinitions/searchstudytitle": {"post": {"tags": ["Common"], "summary": "Search For a Study", "requestBody": {"description": "Parameters to search in database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Common.SearchTitleParameters"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Common.SearchTitleParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Common.SearchTitleParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Common.SearchTitleParameters"}}}}, "responses": {"200": {"description": "Returns All Study that matches the search criteria", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Common.SearchTitleResponse"}}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There is no study that matches the search criteria", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/reports/usage": {"post": {"tags": ["Reports"], "summary": "GET System Usage Report", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Reports.ReportBodyParameters"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Reports.ReportBodyParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Reports.ReportBodyParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Reports.ReportBodyParameters"}}}}, "responses": {"200": {"description": "Returns List of System Usage", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Reports.SystemUsageReport"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Reports.SystemUsageReport"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Reports.SystemUsageReport"}}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There are reports", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v2/studydefinitions/{studyId}": {"get": {"tags": ["StudyV"], "summary": "GET All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "listofelements", "in": "query", "description": "List of elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "usdm-vreison header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "put": {"tags": ["StudyV"], "summary": "POST/PUT All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "USDM Version", "required": true, "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "delete": {"tags": ["StudyV"], "summary": "Delete a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted all versions of Study with the mentioned studyId", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v2/studydesigns": {"get": {"tags": ["StudyV"], "summary": "GET Study Designs of a Study", "parameters": [{"name": "studyId", "in": "query", "description": "Study ID", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "listofelements", "in": "query", "description": "List of study design elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v2/studydefinitions/{studyId}/studydesigns/soa": {"get": {"tags": ["StudyV"], "summary": "GET SoA For a Study USDM Version 1.9", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "scheduleTimelineId", "in": "query", "description": "Schedule Timeline Id", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v2/studyDefinitions/{studyId}/studydesigns/eCPT": {"get": {"tags": ["StudyV"], "summary": "GET eCPT Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studydesignId", "in": "query", "description": "studyDesignId", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "200": {"description": "Returns Study"}}}}, "/v2/studydefinitions": {"post": {"tags": ["StudyV"], "summary": "POST/PUT All Elements For a Study", "parameters": [{"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV2.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studydefinitions/{studyId}": {"get": {"tags": ["StudyV"], "summary": "GET All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "listofelements", "in": "query", "description": "List of elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "usdm-vreison header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "put": {"tags": ["StudyV"], "summary": "PUT All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "USDM Version", "required": true, "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "delete": {"tags": ["StudyV"], "summary": "Delete a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted all versions of Study with the mentioned studyId", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studydesigns": {"get": {"tags": ["StudyV"], "summary": "GET Study Designs of a Study", "parameters": [{"name": "studyId", "in": "query", "description": "Study ID", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "listofelements", "in": "query", "description": "List of study design elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studydefinitions/{studyId}/studydesigns/soa": {"get": {"tags": ["StudyV"], "summary": "GET SoA For a Study USDM Version 2.0", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "scheduleTimelineId", "in": "query", "description": "Schedule Timeline Id", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studyDefinitions/{studyId}/studydesigns/eCPT": {"get": {"tags": ["StudyV"], "summary": "GET eCPT Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studydesignId", "in": "query", "description": "studyDesignId", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "200": {"description": "Returns Study"}}}}, "/v3/studydefinitions/{studyId}/version-comparison": {"get": {"tags": ["StudyV"], "summary": "GET Differences between two versions of a study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdrUploadVersionOne", "in": "query", "description": "First Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "sdrUploadVersionTwo", "in": "query", "description": "Second Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "usdmVersion", "in": "header", "description": "usdm-vreison header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studydefinitions": {"post": {"tags": ["StudyV"], "summary": "POST All Elements For a Study", "parameters": [{"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v3/studydefinitions/validate-usdm-conformance": {"post": {"tags": ["StudyV"], "summary": "Validate USDM Conformance rules for a Study", "parameters": [{"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Validation", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV3.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studydefinitions/{studyId}": {"get": {"tags": ["StudyV"], "summary": "GET All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "listofelements", "in": "query", "description": "List of elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "usdm-vreison header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "put": {"tags": ["StudyV"], "summary": "PUT All Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "USDM Version", "required": true, "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}, "delete": {"tags": ["StudyV"], "summary": "Delete a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Deleted all versions of Study with the mentioned studyId", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studydesigns": {"get": {"tags": ["StudyV"], "summary": "GET Study Designs of a Study", "parameters": [{"name": "studyId", "in": "query", "description": "Study ID", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "listofelements", "in": "query", "description": "List of study design elements with comma separated values", "schema": {"type": "string"}}, {"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studydefinitions/{studyId}/studydesigns/soa": {"get": {"tags": ["StudyV"], "summary": "GET SoA For a Study USDM Version 2.0", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "studyDesignId", "in": "query", "description": "Study Design ID", "schema": {"type": "string"}}, {"name": "scheduleTimelineId", "in": "query", "description": "Schedule Timeline Id", "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studyDefinitions/{studyId}/studydesigns/eCPT": {"get": {"tags": ["StudyV"], "summary": "GET eCPT Elements For a Study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdruploadversion", "in": "query", "description": "Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "studydesignId", "in": "query", "description": "studyDesignId", "schema": {"type": "string"}}], "responses": {"400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "200": {"description": "Returns Study"}}}}, "/v4/studydefinitions/{studyId}/version-comparison": {"get": {"tags": ["StudyV"], "summary": "GET Differences between two versions of a study", "parameters": [{"name": "studyId", "in": "path", "description": "Study ID", "required": true, "schema": {"type": "string"}}, {"name": "sdrUploadVersionOne", "in": "query", "description": "First Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "sdrUploadVersionTwo", "in": "query", "description": "Second Version of study", "schema": {"type": "integer", "format": "int32"}}, {"name": "usdmVersion", "in": "header", "description": "usdm-vreison header", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns Study", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The Study for the studyId is Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studydefinitions": {"post": {"tags": ["StudyV"], "summary": "POST All Elements For a Study", "parameters": [{"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Inserting/Updating in Database", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v4/studydefinitions/validate-usdm-conformance": {"post": {"tags": ["StudyV"], "summary": "Validate USDM Conformance rules for a Study", "parameters": [{"name": "usdmVersion", "in": "header", "description": "USDM Version", "required": true, "schema": {"type": "string"}}], "requestBody": {"description": "Study for Validation", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "text/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "responses": {"201": {"description": "Study Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StudyV4.StudyDefinitions"}}}}, "400": {"description": "Bad Request", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/v1/auth/token": {"post": {"tags": ["Token"], "summary": "GET Token for accessing API's", "requestBody": {"description": "logging user details", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}}}, "responses": {"200": {"description": "Returns Token"}, "400": {"description": "Bad Request"}}}}, "/auth/token": {"post": {"tags": ["Token"], "summary": "GET Token for accessing API's", "requestBody": {"description": "logging user details", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "application/json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Token.UserLogin"}}}}, "responses": {"200": {"description": "Returns Token"}, "400": {"description": "Bad Request"}}}}, "/usergroups/getgroups": {"post": {"tags": ["UserGroups"], "summary": "GET All Groups", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}}}, "responses": {"200": {"description": "Returns List of Groups", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There are no groups in database", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/getusers": {"post": {"tags": ["UserGroups"], "summary": "GET All Users", "requestBody": {"content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupsQueryParameters"}}}}, "responses": {"200": {"description": "Returns List of users in a group", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "There are no users tagged to groups", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/getgrouplist": {"get": {"tags": ["UserGroups"], "summary": "GET group list", "responses": {"200": {"description": "Returns List of users in a group", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The users for the groupId is Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/listusers": {"get": {"tags": ["UserGroups"], "summary": "GET user list from AD", "responses": {"200": {"description": "Returns List of users in a AD", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The users for the groupId is Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/checkgroupname": {"get": {"tags": ["UserGroups"], "summary": "Check Group name", "parameters": [{"name": "groupName", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "Returns a object with the group existance boolean value", "content": {"text/plain": {"schema": {}}, "application/json": {"schema": {}}, "text/json": {"schema": {}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The users for the groupId is Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/postgroup": {"post": {"tags": ["UserGroups"], "summary": "POST a group", "requestBody": {"description": "Group which needs to be added/modified", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserGroups.SDRGroups"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.SDRGroups"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.SDRGroups"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroups.SDRGroups"}}}}, "responses": {"200": {"description": "Returns List of users in a group", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The users for the groupId is Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}, "/usergroups/postuser": {"post": {"tags": ["UserGroups"], "summary": "POST a user to groups", "requestBody": {"description": "User which needs to be added/modified to groups", "content": {"application/json-patch+json": {"schema": {"$ref": "#/components/schemas/UserGroups.PostUserToGroups"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.PostUserToGroups"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.PostUserToGroups"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserGroups.PostUserToGroups"}}}}, "responses": {"200": {"description": "Returns List of users in a group", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserGroups.UserGroupMapping"}}}}, "400": {"description": "Bad Request", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}, "404": {"description": "The users for the groupId is Not Found", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ErrorModels.ErrorModel"}}}}}}}}, "components": {"schemas": {"Common.AuditTrail": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "usdmVersion": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "Common.AuditTrailResponse": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "revisionHistory": {"type": "array", "items": {"$ref": "#/components/schemas/Common.AuditTrailResponseWithLinks"}, "nullable": true}}, "additionalProperties": false}, "Common.AuditTrailResponseWithLinks": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "usdmVersion": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "integer", "format": "int32"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "Common.ChangeAudit": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "changes": {"type": "array", "items": {"$ref": "#/components/schemas/Common.Changes"}, "nullable": true}}, "additionalProperties": false}, "Common.Changes": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "SDRUploadVersion": {"type": "integer", "format": "int32"}, "elements": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "Common.CommonCode": {"type": "object", "properties": {"codeId": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "codeSystem": {"type": "string", "nullable": true}, "codeSystemVersion": {"type": "string", "nullable": true}, "decode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.CommonOrganisation": {"type": "object", "properties": {"organisationId": {"type": "string", "nullable": true}, "organisationIdentifier": {"type": "string", "nullable": true}, "organisationIdentifierScheme": {"type": "string", "nullable": true}, "organisationName": {"type": "string", "nullable": true}, "organisationType": {"$ref": "#/components/schemas/Common.CommonCode"}}, "additionalProperties": false}, "Common.CommonStudyDesign": {"type": "object", "properties": {"interventionModel": {"type": "array", "items": {"$ref": "#/components/schemas/Common.CommonCode"}, "nullable": true}, "studyIndications": {"type": "array", "items": {"$ref": "#/components/schemas/Common.CommonStudyIndication"}, "nullable": true}}, "additionalProperties": false}, "Common.CommonStudyIdentifiers": {"type": "object", "properties": {"studyIdentifierId": {"type": "string", "nullable": true}, "studyIdentifier": {"type": "string", "nullable": true}, "studyIdentifierScope": {"$ref": "#/components/schemas/Common.CommonOrganisation"}}, "additionalProperties": false}, "Common.CommonStudyIndication": {"type": "object", "properties": {"indicationDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.Links": {"type": "object", "properties": {"studyDefinitions": {"type": "string", "nullable": true}, "revisionHistory": {"type": "string", "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/Common.StudyDesignLinks"}, "nullable": true}}, "additionalProperties": false}, "Common.LinksForUI": {"type": "object", "properties": {"studyDefinitions": {"type": "string", "nullable": true}, "revisionHistory": {"type": "string", "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/Common.StudyDesignLinks"}, "nullable": true}, "SoA": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.SearchParameters": {"type": "object", "properties": {"sponsorId": {"type": "string", "nullable": true}, "studyTitle": {"type": "string", "nullable": true}, "indication": {"type": "string", "nullable": true}, "interventionModel": {"type": "string", "nullable": true}, "phase": {"type": "string", "nullable": true}, "usdmVersion": {"type": "string", "nullable": true}, "validateUsdmVersion": {"type": "boolean"}, "fromDate": {"type": "string", "nullable": true}, "toDate": {"type": "string", "nullable": true}, "pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "header": {"type": "string", "nullable": true}, "asc": {"type": "boolean"}}, "additionalProperties": false}, "Common.SearchResponse": {"type": "object", "properties": {"study": {"$ref": "#/components/schemas/Common.SearchStudy"}, "auditTrail": {"$ref": "#/components/schemas/Common.AuditTrail"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "Common.SearchStudy": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "studyTitle": {"type": "string", "nullable": true}, "studyType": {"$ref": "#/components/schemas/Common.CommonCode"}, "studyPhase": {"$ref": "#/components/schemas/Common.CommonCode"}, "studyIdentifiers": {"type": "array", "items": {"$ref": "#/components/schemas/Common.CommonStudyIdentifiers"}, "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/Common.CommonStudyDesign"}, "nullable": true}}, "additionalProperties": false}, "Common.SearchTitleAuditTrail": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "SDRUploadVersion": {"type": "integer", "format": "int32"}, "usdmVersion": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.SearchTitleParameters": {"type": "object", "properties": {"studyTitle": {"type": "string", "nullable": true}, "sponsorId": {"type": "string", "nullable": true}, "fromDate": {"type": "string", "nullable": true}, "toDate": {"type": "string", "nullable": true}, "pageSize": {"type": "integer", "format": "int32"}, "pageNumber": {"type": "integer", "format": "int32"}, "groupByStudyId": {"type": "boolean"}, "sortOrder": {"type": "string", "nullable": true}, "sortBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.SearchTitleResponse": {"type": "object", "properties": {"study": {"$ref": "#/components/schemas/Common.SearchTitleStudy"}, "auditTrail": {"$ref": "#/components/schemas/Common.SearchTitleAuditTrail"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "Common.SearchTitleStudy": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "studyTitle": {"type": "string", "nullable": true}, "studyIdentifiers": {"type": "array", "items": {"$ref": "#/components/schemas/Common.CommonStudyIdentifiers"}, "nullable": true}}, "additionalProperties": false}, "Common.StudyDesignLinks": {"type": "object", "properties": {"studyDesignId": {"type": "string", "nullable": true}, "studyDesignLink": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Common.StudyHistoryResponse": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "array", "items": {"$ref": "#/components/schemas/Common.UploadVersion"}, "nullable": true}}, "additionalProperties": false}, "Common.UploadVersion": {"type": "object", "properties": {"uploadVersion": {"type": "integer", "format": "int32"}, "entryDateTime": {"type": "string", "format": "date-time"}, "studyTitle": {"nullable": true}, "studyVersion": {"type": "string", "nullable": true}, "studyIdentifiers": {"nullable": true}, "protocolVersions": {"type": "array", "items": {"type": "string"}, "nullable": true}, "usdmVersion": {"type": "string", "nullable": true}, "links": {"$ref": "#/components/schemas/Common.Links"}}, "additionalProperties": false}, "ErrorModels.ErrorModel": {"type": "object", "properties": {"statusCode": {"type": "string", "nullable": true}, "message": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Reports.ReportBodyParameters": {"type": "object", "properties": {"days": {"type": "integer", "format": "int32"}, "operation": {"type": "string", "nullable": true}, "responseCode": {"type": "integer", "format": "int32"}, "recordNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sortBy": {"type": "string", "nullable": true}, "sortOrder": {"type": "string", "nullable": true}, "filterByTime": {"type": "boolean"}, "fromDateTime": {"type": "string", "format": "date-time"}, "toDateTime": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Reports.SystemUsageReport": {"type": "object", "properties": {"emailId": {"type": "string", "nullable": true}, "userName": {"type": "string", "nullable": true}, "operation": {"type": "string", "nullable": true}, "api": {"type": "string", "nullable": true}, "requestDate": {"type": "string", "nullable": true}, "callerIpAddress": {"type": "string", "nullable": true}, "responseCode": {"type": "string", "nullable": true}, "responseCodeDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Activity": {"type": "object", "properties": {"activityId": {"type": "string", "nullable": true}, "activityDescription": {"type": "string", "nullable": true}, "activityName": {"type": "string", "nullable": true}, "definedProcedures": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Procedure"}, "nullable": true}, "nextActivityId": {"type": "string", "nullable": true}, "previousActivityId": {"type": "string", "nullable": true}, "activityIsConditional": {"nullable": true}, "activityIsConditionalReason": {"type": "string", "nullable": true}, "bcCategoryIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcSurrogateIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "biomedicalConceptIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "activityTimelineId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Address": {"type": "object", "properties": {"text": {"type": "string", "nullable": true}, "line": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"$ref": "#/components/schemas/StudyV2.Code"}}, "additionalProperties": false}, "StudyV2.AliasCode": {"type": "object", "properties": {"aliasCodeId": {"type": "string", "nullable": true}, "standardCode": {"$ref": "#/components/schemas/StudyV2.Code"}, "standardCodeAliases": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.AnalysisPopulation": {"type": "object", "properties": {"analysisPopulationId": {"type": "string", "nullable": true}, "populationDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.AuditTrail": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "usdmVersion": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StudyV2.BiomedicalConcept": {"type": "object", "properties": {"biomedicalConceptId": {"type": "string", "nullable": true}, "bcName": {"type": "string", "nullable": true}, "bcSynonyms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcReference": {"type": "string", "nullable": true}, "bcProperties": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.BiomedicalConceptProperty"}, "nullable": true}, "bcConceptCode": {"$ref": "#/components/schemas/StudyV2.AliasCode"}}, "additionalProperties": false}, "StudyV2.BiomedicalConceptCategory": {"type": "object", "properties": {"biomedicalConceptCategoryId": {"type": "string", "nullable": true}, "bcCategoryParentIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcCategoryChildrenIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcCategoryName": {"type": "string", "nullable": true}, "bcCategoryDescription": {"type": "string", "nullable": true}, "bcCategoryMemberIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.BiomedicalConceptProperty": {"type": "object", "properties": {"bcPropertyId": {"type": "string", "nullable": true}, "bcPropertyName": {"type": "string", "nullable": true}, "bcPropertyRequired": {"nullable": true}, "bcPropertyEnabled": {"nullable": true}, "bcPropertyDataType": {"type": "string", "nullable": true}, "bcPropertyResponseCodes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.ResponseCode"}, "nullable": true}, "bcPropertyConceptCode": {"$ref": "#/components/schemas/StudyV2.AliasCode"}}, "additionalProperties": false}, "StudyV2.BiomedicalConceptSurrogate": {"type": "object", "properties": {"bcSurrogateId": {"type": "string", "nullable": true}, "bcSurrogateName": {"type": "string", "nullable": true}, "bcSurrogateDescription": {"type": "string", "nullable": true}, "bcSurrogateReference": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Code": {"type": "object", "properties": {"codeId": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "codeSystem": {"type": "string", "nullable": true}, "codeSystemVersion": {"type": "string", "nullable": true}, "decode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Encounter": {"type": "object", "properties": {"encounterId": {"type": "string", "nullable": true}, "encounterContactModes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}, "encounterDescription": {"type": "string", "nullable": true}, "encounterEnvironmentalSetting": {"$ref": "#/components/schemas/StudyV2.Code"}, "encounterName": {"type": "string", "nullable": true}, "encounterType": {"$ref": "#/components/schemas/StudyV2.Code"}, "nextEncounterId": {"type": "string", "nullable": true}, "previousEncounterId": {"type": "string", "nullable": true}, "encounterScheduledAtTimingId": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV2.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV2.TransitionRule"}}, "additionalProperties": false}, "StudyV2.Endpoint": {"type": "object", "properties": {"endpointId": {"type": "string", "nullable": true}, "endpointDescription": {"type": "string", "nullable": true}, "endpointPurposeDescription": {"type": "string", "nullable": true}, "endpointLevel": {"$ref": "#/components/schemas/StudyV2.Code"}}, "additionalProperties": false}, "StudyV2.Estimand": {"type": "object", "properties": {"estimandId": {"type": "string", "nullable": true}, "treatment": {"type": "string", "nullable": true}, "summaryMeasure": {"type": "string", "nullable": true}, "analysisPopulation": {"$ref": "#/components/schemas/StudyV2.AnalysisPopulation"}, "variableOfInterest": {"type": "string", "nullable": true}, "intercurrentEvents": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.InterCurrentEvent"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.Indication": {"type": "object", "properties": {"indicationId": {"type": "string", "nullable": true}, "indicationDescription": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.InterCurrentEvent": {"type": "object", "properties": {"intercurrentEventId": {"type": "string", "nullable": true}, "intercurrentEventDescription": {"type": "string", "nullable": true}, "intercurrentEventName": {"type": "string", "nullable": true}, "intercurrentEventStrategy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.InvestigationalIntervention": {"type": "object", "properties": {"investigationalInterventionId": {"type": "string", "nullable": true}, "interventionDescription": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.Objective": {"type": "object", "properties": {"objectiveId": {"type": "string", "nullable": true}, "objectiveDescription": {"type": "string", "nullable": true}, "objectiveLevel": {"$ref": "#/components/schemas/StudyV2.Code"}, "objectiveEndpoints": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Endpoint"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.Organisation": {"type": "object", "properties": {"organisationId": {"type": "string", "nullable": true}, "organisationIdentifier": {"type": "string", "nullable": true}, "organisationIdentifierScheme": {"type": "string", "nullable": true}, "organisationName": {"type": "string", "nullable": true}, "organisationType": {"$ref": "#/components/schemas/StudyV2.Code"}, "organizationLegalAddress": {"$ref": "#/components/schemas/StudyV2.Address"}}, "additionalProperties": false}, "StudyV2.Procedure": {"type": "object", "properties": {"procedureId": {"type": "string", "nullable": true}, "procedureCode": {"$ref": "#/components/schemas/StudyV2.Code"}, "procedureType": {"type": "string", "nullable": true}, "procedureIsConditional": {"nullable": true}, "procedureIsConditionalReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.ResponseCode": {"type": "object", "properties": {"responseCodeId": {"type": "string", "nullable": true}, "responseCodeEnabled": {"nullable": true}, "code": {"$ref": "#/components/schemas/StudyV2.Code"}}, "additionalProperties": false}, "StudyV2.ScheduleTimeline": {"type": "object", "properties": {"scheduleTimelineId": {"type": "string", "nullable": true}, "scheduleTimelineName": {"type": "string", "nullable": true}, "scheduleTimelineDescription": {"type": "string", "nullable": true}, "entryCondition": {"type": "string", "nullable": true}, "scheduleTimelineEntryId": {"type": "string", "nullable": true}, "scheduleTimelineExits": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.ScheduleTimelineExit"}, "nullable": true}, "scheduleTimelineInstances": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.ScheduledInstance"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.ScheduleTimelineExit": {"type": "object", "properties": {"scheduleTimelineExitId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.ScheduledInstance": {"type": "object", "properties": {"scheduledInstanceId": {"type": "string", "nullable": true}, "scheduleTimelineExitId": {"type": "string", "nullable": true}, "scheduledInstanceEncounterId": {"type": "string", "nullable": true}, "scheduledInstanceTimelineId": {"type": "string", "nullable": true}, "scheduleSequenceNumber": {"nullable": true}, "scheduledInstanceTimings": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Timing"}, "nullable": true}, "scheduledInstanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Study": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "studyTitle": {"type": "string", "nullable": true}, "studyVersion": {"type": "string", "nullable": true}, "studyType": {"$ref": "#/components/schemas/StudyV2.Code"}, "studyRationale": {"type": "string", "nullable": true}, "studyAcronym": {"type": "string", "nullable": true}, "studyIdentifiers": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyIdentifier"}, "nullable": true}, "studyPhase": {"$ref": "#/components/schemas/StudyV2.AliasCode"}, "businessTherapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}, "studyProtocolVersions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyProtocolVersion"}, "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyDesign"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.StudyArm": {"type": "object", "properties": {"studyArmId": {"type": "string", "nullable": true}, "studyArmDataOriginDescription": {"type": "string", "nullable": true}, "studyArmDataOriginType": {"$ref": "#/components/schemas/StudyV2.Code"}, "studyArmDescription": {"type": "string", "nullable": true}, "studyArmName": {"type": "string", "nullable": true}, "studyArmType": {"$ref": "#/components/schemas/StudyV2.Code"}}, "additionalProperties": false}, "StudyV2.StudyCell": {"type": "object", "properties": {"studyCellId": {"type": "string", "nullable": true}, "studyArm": {"$ref": "#/components/schemas/StudyV2.StudyArm"}, "studyEpoch": {"$ref": "#/components/schemas/StudyV2.StudyEpoch"}, "studyElements": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyElement"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.StudyDefinitions": {"type": "object", "properties": {"clinicalStudy": {"$ref": "#/components/schemas/StudyV2.Study"}, "auditTrail": {"$ref": "#/components/schemas/StudyV2.AuditTrail"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "StudyV2.StudyDesign": {"type": "object", "properties": {"studyDesignId": {"type": "string", "nullable": true}, "studyDesignName": {"type": "string", "nullable": true}, "studyDesignDescription": {"type": "string", "nullable": true}, "trialIntentType": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}, "trialType": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}, "interventionModel": {"$ref": "#/components/schemas/StudyV2.Code"}, "studyCells": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyCell"}, "nullable": true}, "studyIndications": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Indication"}, "nullable": true}, "studyInvestigationalInterventions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.InvestigationalIntervention"}, "nullable": true}, "studyPopulations": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.StudyDesignPopulation"}, "nullable": true}, "studyObjectives": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Objective"}, "nullable": true}, "studyScheduleTimelines": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.ScheduleTimeline"}, "nullable": true}, "therapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}, "studyEstimands": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Estimand"}, "nullable": true}, "encounters": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Encounter"}, "nullable": true}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Activity"}, "nullable": true}, "studyDesignRationale": {"type": "string", "nullable": true}, "studyDesignBlindingScheme": {"$ref": "#/components/schemas/StudyV2.AliasCode"}, "biomedicalConcepts": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.BiomedicalConcept"}, "nullable": true}, "bcCategories": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.BiomedicalConceptCategory"}, "nullable": true}, "bcSurrogates": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.BiomedicalConceptSurrogate"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.StudyDesignPopulation": {"type": "object", "properties": {"studyDesignPopulationId": {"type": "string", "nullable": true}, "populationDescription": {"type": "string", "nullable": true}, "plannedNumberOfParticipants": {"nullable": true}, "plannedMaximumAgeOfParticipants": {"type": "string", "nullable": true}, "plannedMinimumAgeOfParticipants": {"type": "string", "nullable": true}, "plannedSexOfParticipants": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV2.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.StudyElement": {"type": "object", "properties": {"studyElementId": {"type": "string", "nullable": true}, "studyElementDescription": {"type": "string", "nullable": true}, "studyElementName": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV2.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV2.TransitionRule"}}, "additionalProperties": false}, "StudyV2.StudyEpoch": {"type": "object", "properties": {"studyEpochId": {"type": "string", "nullable": true}, "nextStudyEpochId": {"type": "string", "nullable": true}, "previousStudyEpochId": {"type": "string", "nullable": true}, "studyEpochDescription": {"type": "string", "nullable": true}, "studyEpochName": {"type": "string", "nullable": true}, "studyEpochType": {"$ref": "#/components/schemas/StudyV2.Code"}, "encounters": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "StudyV2.StudyIdentifier": {"type": "object", "properties": {"studyIdentifierId": {"type": "string", "nullable": true}, "studyIdentifier": {"type": "string", "nullable": true}, "studyIdentifierScope": {"$ref": "#/components/schemas/StudyV2.Organisation"}}, "additionalProperties": false}, "StudyV2.StudyProtocolVersion": {"type": "object", "properties": {"studyProtocolVersionId": {"type": "string", "nullable": true}, "briefTitle": {"type": "string", "nullable": true}, "officialTitle": {"type": "string", "nullable": true}, "protocolAmendment": {"type": "string", "nullable": true}, "protocolEffectiveDate": {"type": "string", "nullable": true}, "protocolStatus": {"$ref": "#/components/schemas/StudyV2.Code"}, "protocolVersion": {"type": "string", "nullable": true}, "publicTitle": {"type": "string", "nullable": true}, "scientificTitle": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV2.Timing": {"type": "object", "properties": {"timingId": {"type": "string", "nullable": true}, "timingType": {"$ref": "#/components/schemas/StudyV2.Code"}, "timingValue": {"type": "string", "nullable": true}, "timingWindow": {"type": "string", "nullable": true}, "relativeToScheduledInstanceId": {"type": "string", "nullable": true}, "relativeFromScheduledInstanceId": {"type": "string", "nullable": true}, "timingRelativeToFrom": {"$ref": "#/components/schemas/StudyV2.Code"}}, "additionalProperties": false}, "StudyV2.TransitionRule": {"type": "object", "properties": {"transitionRuleId": {"type": "string", "nullable": true}, "transitionRuleDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Activity": {"type": "object", "properties": {"activityId": {"type": "string", "nullable": true}, "activityDescription": {"type": "string", "nullable": true}, "activityName": {"type": "string", "nullable": true}, "definedProcedures": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Procedure"}, "nullable": true}, "nextActivityId": {"type": "string", "nullable": true}, "previousActivityId": {"type": "string", "nullable": true}, "activityIsConditional": {"nullable": true}, "activityIsConditionalReason": {"type": "string", "nullable": true}, "bcCategoryIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcSurrogateIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "biomedicalConceptIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "activityTimelineId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Address": {"type": "object", "properties": {"addressId": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "line": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.AliasCode": {"type": "object", "properties": {"aliasCodeId": {"type": "string", "nullable": true}, "standardCode": {"$ref": "#/components/schemas/StudyV3.Code"}, "standardCodeAliases": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.AnalysisPopulation": {"type": "object", "properties": {"analysisPopulationId": {"type": "string", "nullable": true}, "populationDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.AuditTrail": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "usdmVersion": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StudyV3.BiomedicalConcept": {"type": "object", "properties": {"biomedicalConceptId": {"type": "string", "nullable": true}, "bcName": {"type": "string", "nullable": true}, "bcSynonyms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcReference": {"type": "string", "nullable": true}, "bcProperties": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.BiomedicalConceptProperty"}, "nullable": true}, "bcConceptCode": {"$ref": "#/components/schemas/StudyV3.AliasCode"}}, "additionalProperties": false}, "StudyV3.BiomedicalConceptCategory": {"type": "object", "properties": {"biomedicalConceptCategoryId": {"type": "string", "nullable": true}, "bcCategoryChildIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcCategoryName": {"type": "string", "nullable": true}, "bcCategoryDescription": {"type": "string", "nullable": true}, "bcCategoryMemberIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcCategoryCode": {"$ref": "#/components/schemas/StudyV3.AliasCode"}}, "additionalProperties": false}, "StudyV3.BiomedicalConceptProperty": {"type": "object", "properties": {"bcPropertyId": {"type": "string", "nullable": true}, "bcPropertyName": {"type": "string", "nullable": true}, "bcPropertyRequired": {"nullable": true}, "bcPropertyEnabled": {"nullable": true}, "bcPropertyDataType": {"type": "string", "nullable": true}, "bcPropertyResponseCodes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.ResponseCode"}, "nullable": true}, "bcPropertyConceptCode": {"$ref": "#/components/schemas/StudyV3.AliasCode"}}, "additionalProperties": false}, "StudyV3.BiomedicalConceptSurrogate": {"type": "object", "properties": {"bcSurrogateId": {"type": "string", "nullable": true}, "bcSurrogateName": {"type": "string", "nullable": true}, "bcSurrogateDescription": {"type": "string", "nullable": true}, "bcSurrogateReference": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Code": {"type": "object", "properties": {"codeId": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "codeSystem": {"type": "string", "nullable": true}, "codeSystemVersion": {"type": "string", "nullable": true}, "decode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Encounter": {"type": "object", "properties": {"encounterId": {"type": "string", "nullable": true}, "encounterContactModes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}, "encounterDescription": {"type": "string", "nullable": true}, "encounterEnvironmentalSetting": {"$ref": "#/components/schemas/StudyV3.Code"}, "encounterName": {"type": "string", "nullable": true}, "encounterType": {"$ref": "#/components/schemas/StudyV3.Code"}, "nextEncounterId": {"type": "string", "nullable": true}, "previousEncounterId": {"type": "string", "nullable": true}, "encounterScheduledAtTimingId": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV3.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV3.TransitionRule"}}, "additionalProperties": false}, "StudyV3.Endpoint": {"type": "object", "properties": {"endpointId": {"type": "string", "nullable": true}, "endpointDescription": {"type": "string", "nullable": true}, "endpointPurposeDescription": {"type": "string", "nullable": true}, "endpointLevel": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.Estimand": {"type": "object", "properties": {"estimandId": {"type": "string", "nullable": true}, "treatment": {"type": "string", "nullable": true}, "summaryMeasure": {"type": "string", "nullable": true}, "analysisPopulation": {"$ref": "#/components/schemas/StudyV3.AnalysisPopulation"}, "variableOfInterest": {"type": "string", "nullable": true}, "intercurrentEvents": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.InterCurrentEvent"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.Indication": {"type": "object", "properties": {"indicationId": {"type": "string", "nullable": true}, "indicationDescription": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.InterCurrentEvent": {"type": "object", "properties": {"intercurrentEventId": {"type": "string", "nullable": true}, "intercurrentEventDescription": {"type": "string", "nullable": true}, "intercurrentEventName": {"type": "string", "nullable": true}, "intercurrentEventStrategy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.InvestigationalIntervention": {"type": "object", "properties": {"investigationalInterventionId": {"type": "string", "nullable": true}, "interventionDescription": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.Objective": {"type": "object", "properties": {"objectiveId": {"type": "string", "nullable": true}, "objectiveDescription": {"type": "string", "nullable": true}, "objectiveLevel": {"$ref": "#/components/schemas/StudyV3.Code"}, "objectiveEndpoints": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Endpoint"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.Organisation": {"type": "object", "properties": {"organisationId": {"type": "string", "nullable": true}, "organisationIdentifier": {"type": "string", "nullable": true}, "organisationIdentifierScheme": {"type": "string", "nullable": true}, "organisationName": {"type": "string", "nullable": true}, "organisationType": {"$ref": "#/components/schemas/StudyV3.Code"}, "organizationLegalAddress": {"$ref": "#/components/schemas/StudyV3.Address"}}, "additionalProperties": false}, "StudyV3.Procedure": {"type": "object", "properties": {"procedureId": {"type": "string", "nullable": true}, "procedureName": {"type": "string", "nullable": true}, "procedureDescription": {"type": "string", "nullable": true}, "procedureType": {"type": "string", "nullable": true}, "procedureCode": {"$ref": "#/components/schemas/StudyV3.Code"}, "procedureIsConditional": {"nullable": true}, "procedureIsConditionalReason": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.ResponseCode": {"type": "object", "properties": {"responseCodeId": {"type": "string", "nullable": true}, "responseCodeEnabled": {"nullable": true}, "code": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.ScheduleTimeline": {"type": "object", "properties": {"scheduleTimelineId": {"type": "string", "nullable": true}, "scheduleTimelineName": {"type": "string", "nullable": true}, "scheduleTimelineDescription": {"type": "string", "nullable": true}, "entryCondition": {"type": "string", "nullable": true}, "scheduleTimelineEntryId": {"type": "string", "nullable": true}, "scheduleTimelineExits": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.ScheduleTimelineExit"}, "nullable": true}, "mainTimeline": {"nullable": true}, "scheduleTimelineInstances": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.ScheduledInstance"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.ScheduleTimelineExit": {"type": "object", "properties": {"scheduleTimelineExitId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.ScheduledInstance": {"type": "object", "properties": {"scheduledInstanceId": {"type": "string", "nullable": true}, "scheduleTimelineExitId": {"type": "string", "nullable": true}, "scheduledInstanceTimelineId": {"type": "string", "nullable": true}, "defaultConditionId": {"type": "string", "nullable": true}, "epochId": {"type": "string", "nullable": true}, "scheduledInstanceTimings": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Timing"}, "nullable": true}, "scheduledInstanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Study": {"type": "object", "properties": {"studyId": {"type": "string", "nullable": true}, "studyTitle": {"type": "string", "nullable": true}, "studyVersion": {"type": "string", "nullable": true}, "studyType": {"$ref": "#/components/schemas/StudyV3.Code"}, "studyRationale": {"type": "string", "nullable": true}, "studyAcronym": {"type": "string", "nullable": true}, "studyIdentifiers": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyIdentifier"}, "nullable": true}, "studyPhase": {"$ref": "#/components/schemas/StudyV3.AliasCode"}, "businessTherapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}, "studyProtocolVersions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyProtocolVersion"}, "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyDesign"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.StudyArm": {"type": "object", "properties": {"studyArmId": {"type": "string", "nullable": true}, "studyArmDataOriginDescription": {"type": "string", "nullable": true}, "studyArmDataOriginType": {"$ref": "#/components/schemas/StudyV3.Code"}, "studyArmDescription": {"type": "string", "nullable": true}, "studyArmName": {"type": "string", "nullable": true}, "studyArmType": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.StudyCell": {"type": "object", "properties": {"studyCellId": {"type": "string", "nullable": true}, "studyArmId": {"type": "string", "nullable": true}, "studyEpochId": {"type": "string", "nullable": true}, "studyElementIds": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.StudyDefinitions": {"type": "object", "properties": {"study": {"$ref": "#/components/schemas/StudyV3.Study"}, "auditTrail": {"$ref": "#/components/schemas/StudyV3.AuditTrail"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "StudyV3.StudyDesign": {"type": "object", "properties": {"studyDesignId": {"type": "string", "nullable": true}, "studyDesignName": {"type": "string", "nullable": true}, "studyDesignDescription": {"type": "string", "nullable": true}, "trialIntentTypes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}, "trialType": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}, "interventionModel": {"$ref": "#/components/schemas/StudyV3.Code"}, "studyCells": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyCell"}, "nullable": true}, "studyIndications": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Indication"}, "nullable": true}, "studyInvestigationalInterventions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.InvestigationalIntervention"}, "nullable": true}, "studyPopulations": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyDesignPopulation"}, "nullable": true}, "studyObjectives": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Objective"}, "nullable": true}, "studyScheduleTimelines": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.ScheduleTimeline"}, "nullable": true}, "therapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}, "studyEstimands": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Estimand"}, "nullable": true}, "encounters": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Encounter"}, "nullable": true}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Activity"}, "nullable": true}, "studyDesignRationale": {"type": "string", "nullable": true}, "studyDesignBlindingScheme": {"$ref": "#/components/schemas/StudyV3.AliasCode"}, "biomedicalConcepts": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.BiomedicalConcept"}, "nullable": true}, "bcCategories": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.BiomedicalConceptCategory"}, "nullable": true}, "bcSurrogates": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.BiomedicalConceptSurrogate"}, "nullable": true}, "studyArms": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyArm"}, "nullable": true}, "studyEpochs": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyEpoch"}, "nullable": true}, "studyElements": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.StudyElement"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.StudyDesignPopulation": {"type": "object", "properties": {"studyDesignPopulationId": {"type": "string", "nullable": true}, "populationDescription": {"type": "string", "nullable": true}, "plannedNumberOfParticipants": {"nullable": true}, "plannedMaximumAgeOfParticipants": {"type": "string", "nullable": true}, "plannedMinimumAgeOfParticipants": {"type": "string", "nullable": true}, "plannedSexOfParticipants": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV3.Code"}, "nullable": true}}, "additionalProperties": false}, "StudyV3.StudyElement": {"type": "object", "properties": {"studyElementId": {"type": "string", "nullable": true}, "studyElementDescription": {"type": "string", "nullable": true}, "studyElementName": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV3.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV3.TransitionRule"}}, "additionalProperties": false}, "StudyV3.StudyEpoch": {"type": "object", "properties": {"studyEpochId": {"type": "string", "nullable": true}, "nextStudyEpochId": {"type": "string", "nullable": true}, "previousStudyEpochId": {"type": "string", "nullable": true}, "studyEpochDescription": {"type": "string", "nullable": true}, "studyEpochName": {"type": "string", "nullable": true}, "studyEpochType": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.StudyIdentifier": {"type": "object", "properties": {"studyIdentifierId": {"type": "string", "nullable": true}, "studyIdentifier": {"type": "string", "nullable": true}, "studyIdentifierScope": {"$ref": "#/components/schemas/StudyV3.Organisation"}}, "additionalProperties": false}, "StudyV3.StudyProtocolVersion": {"type": "object", "properties": {"studyProtocolVersionId": {"type": "string", "nullable": true}, "briefTitle": {"type": "string", "nullable": true}, "officialTitle": {"type": "string", "nullable": true}, "protocolAmendment": {"type": "string", "nullable": true}, "protocolEffectiveDate": {"type": "string", "nullable": true}, "protocolStatus": {"$ref": "#/components/schemas/StudyV3.Code"}, "protocolVersion": {"type": "string", "nullable": true}, "publicTitle": {"type": "string", "nullable": true}, "scientificTitle": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV3.Timing": {"type": "object", "properties": {"timingId": {"type": "string", "nullable": true}, "timingType": {"$ref": "#/components/schemas/StudyV3.Code"}, "timingValue": {"type": "string", "nullable": true}, "timingDescription": {"type": "string", "nullable": true}, "timingWindow": {"type": "string", "nullable": true}, "relativeToScheduledInstanceId": {"type": "string", "nullable": true}, "relativeFromScheduledInstanceId": {"type": "string", "nullable": true}, "timingWindowLower": {"type": "string", "nullable": true}, "timingWindowUpper": {"type": "string", "nullable": true}, "timingRelativeToFrom": {"$ref": "#/components/schemas/StudyV3.Code"}}, "additionalProperties": false}, "StudyV3.TransitionRule": {"type": "object", "properties": {"transitionRuleId": {"type": "string", "nullable": true}, "transitionRuleDescription": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Activity": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "definedProcedures": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Procedure"}, "nullable": true}, "nextId": {"type": "string", "nullable": true}, "previousId": {"type": "string", "nullable": true}, "bcCategoryIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "bcSurrogateIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "biomedicalConceptIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "timelineId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Address": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "line": {"type": "string", "nullable": true}, "city": {"type": "string", "nullable": true}, "district": {"type": "string", "nullable": true}, "state": {"type": "string", "nullable": true}, "postalCode": {"type": "string", "nullable": true}, "country": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.AdministrationDuration": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "quantity": {"$ref": "#/components/schemas/StudyV4.Quantity"}, "description": {"type": "string", "nullable": true}, "durationWillVary": {"nullable": true}, "reasonDurationWillVary": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.AgentAdministration": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "duration": {"$ref": "#/components/schemas/StudyV4.AdministrationDuration"}, "dose": {"$ref": "#/components/schemas/StudyV4.Quantity"}, "route": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "frequency": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.AliasCode": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "standardCode": {"$ref": "#/components/schemas/StudyV4.Code"}, "standardCodeAliases": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.AnalysisPopulation": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.AuditTrail": {"type": "object", "properties": {"entryDateTime": {"type": "string", "format": "date-time"}, "usdmVersion": {"type": "string", "nullable": true}, "SDRUploadVersion": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "StudyV4.BiomedicalConcept": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "synonyms": {"type": "array", "items": {"type": "string"}, "nullable": true}, "reference": {"type": "string", "nullable": true}, "properties": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.BiomedicalConceptProperty"}, "nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.BiomedicalConceptCategory": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "childIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "memberIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.BiomedicalConceptProperty": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "isRequired": {"nullable": true}, "isEnabled": {"nullable": true}, "datatype": {"type": "string", "nullable": true}, "responseCodes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.ResponseCode"}, "nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.BiomedicalConceptSurrogate": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "reference": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Characteristic": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "dictionaryId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Code": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}, "codeSystem": {"type": "string", "nullable": true}, "codeSystemVersion": {"type": "string", "nullable": true}, "decode": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Condition": {"type": "object", "properties": {"contextIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "appliesToIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "dictionaryId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.EligibilityCriterion": {"type": "object", "properties": {"category": {"$ref": "#/components/schemas/StudyV4.Code"}, "identifier": {"type": "string", "nullable": true}, "previousId": {"type": "string", "nullable": true}, "nextId": {"type": "string", "nullable": true}, "contextId": {"type": "string", "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "dictionaryId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Encounter": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "contactModes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "environmentalSetting": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "nextId": {"type": "string", "nullable": true}, "previousId": {"type": "string", "nullable": true}, "scheduledAtTimingId": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV4.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV4.TransitionRule"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Endpoint": {"type": "object", "properties": {"purpose": {"type": "string", "nullable": true}, "level": {"$ref": "#/components/schemas/StudyV4.Code"}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "dictionaryId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Estimand": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "interventionId": {"type": "string", "nullable": true}, "summaryMeasure": {"type": "string", "nullable": true}, "analysisPopulation": {"$ref": "#/components/schemas/StudyV4.AnalysisPopulation"}, "variableOfInterestId": {"type": "string", "nullable": true}, "intercurrentEvents": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.IntercurrentEvent"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.GeographicScope": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "code": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.GovernanceDate": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "dateValue": {"type": "string", "nullable": true}, "geographicScopes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.GeographicScope"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Indication": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.IntercurrentEvent": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "strategy": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Masking": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "role": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.NarrativeContent": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "sectionNumber": {"type": "string", "nullable": true}, "sectionTitle": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "childIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "previousId": {"type": "string", "nullable": true}, "nextId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Objective": {"type": "object", "properties": {"level": {"$ref": "#/components/schemas/StudyV4.Code"}, "endpoints": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Endpoint"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "dictionaryId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Organization": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "identifier": {"type": "string", "nullable": true}, "identifierScheme": {"type": "string", "nullable": true}, "organizationType": {"$ref": "#/components/schemas/StudyV4.Code"}, "legalAddress": {"$ref": "#/components/schemas/StudyV4.Address"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Procedure": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "procedureType": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.Code"}, "studyInterventionId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Quantity": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "unit": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "value": {"nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Range": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "minValue": {"nullable": true}, "maxValue": {"nullable": true}, "unit": {"$ref": "#/components/schemas/StudyV4.Code"}, "isApproximate": {"nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.ResearchOrganization": {"type": "object", "properties": {"manages": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudySite"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "identifier": {"type": "string", "nullable": true}, "identifierScheme": {"type": "string", "nullable": true}, "organizationType": {"$ref": "#/components/schemas/StudyV4.Code"}, "legalAddress": {"$ref": "#/components/schemas/StudyV4.Address"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.ResponseCode": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "isEnabled": {"nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.ScheduleTimeline": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "entryCondition": {"type": "string", "nullable": true}, "entryId": {"type": "string", "nullable": true}, "exits": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.ScheduleTimelineExit"}, "nullable": true}, "mainTimeline": {"nullable": true}, "timings": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Timing"}, "nullable": true}, "instances": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.ScheduledInstance"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.ScheduleTimelineExit": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.ScheduledInstance": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "timelineExitId": {"type": "string", "nullable": true}, "timelineId": {"type": "string", "nullable": true}, "defaultConditionId": {"type": "string", "nullable": true}, "epochId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Study": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "versions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyVersion"}, "nullable": true}, "documentedBy": {"$ref": "#/components/schemas/StudyV4.StudyProtocolDocument"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyAmendment": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "number": {"type": "string", "nullable": true}, "summary": {"type": "string", "nullable": true}, "substantialImpact": {"type": "boolean"}, "previousId": {"type": "string", "nullable": true}, "enrollments": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.SubjectEnrollment"}, "nullable": true}, "primaryReason": {"$ref": "#/components/schemas/StudyV4.StudyAmendmentReason"}, "secondaryReasons": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyAmendmentReason"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyAmendmentReason": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "code": {"$ref": "#/components/schemas/StudyV4.Code"}, "otherReason": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyArm": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "dataOriginDescription": {"type": "string", "nullable": true}, "dataOriginType": {"$ref": "#/components/schemas/StudyV4.Code"}, "populationIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyCell": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "armId": {"type": "string", "nullable": true}, "epochId": {"type": "string", "nullable": true}, "elementIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyCohort": {"type": "object", "properties": {"characteristics": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Characteristic"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "includesHealthySubjects": {"type": "boolean"}, "plannedAge": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedCompletionNumber": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedEnrollmentNumber": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedSex": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "criteria": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.EligibilityCriterion"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyDefinitions": {"type": "object", "properties": {"study": {"$ref": "#/components/schemas/StudyV4.Study"}, "usdmVersion": {"type": "string", "nullable": true}, "systemName": {"type": "string", "nullable": true}, "systemVersion": {"type": "string", "nullable": true}, "auditTrail": {"$ref": "#/components/schemas/StudyV4.AuditTrail"}, "links": {"$ref": "#/components/schemas/Common.LinksForUI"}}, "additionalProperties": false}, "StudyV4.StudyDesign": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "trialIntentTypes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "trialTypes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "characteristics": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "interventionModel": {"$ref": "#/components/schemas/StudyV4.Code"}, "studyCells": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyCell"}, "nullable": true}, "indications": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Indication"}, "nullable": true}, "studyInterventions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyIntervention"}, "nullable": true}, "population": {"$ref": "#/components/schemas/StudyV4.StudyDesignPopulation"}, "objectives": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Objective"}, "nullable": true}, "scheduleTimelines": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.ScheduleTimeline"}, "nullable": true}, "therapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "estimands": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Estimand"}, "nullable": true}, "encounters": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Encounter"}, "nullable": true}, "activities": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Activity"}, "nullable": true}, "rationale": {"type": "string", "nullable": true}, "blindingSchema": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "biomedicalConcepts": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.BiomedicalConcept"}, "nullable": true}, "bcCategories": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.BiomedicalConceptCategory"}, "nullable": true}, "bcSurrogates": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.BiomedicalConceptSurrogate"}, "nullable": true}, "arms": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyArm"}, "nullable": true}, "epochs": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyEpoch"}, "nullable": true}, "elements": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyElement"}, "nullable": true}, "documentVersionId": {"type": "string", "nullable": true}, "dictionaries": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.SyntaxTemplateDictionary"}, "nullable": true}, "maskingRoles": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Masking"}, "nullable": true}, "conditions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Condition"}, "nullable": true}, "organizations": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.ResearchOrganization"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyDesignPopulation": {"type": "object", "properties": {"cohorts": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyCohort"}, "nullable": true}, "id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "includesHealthySubjects": {"type": "boolean"}, "plannedAge": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedCompletionNumber": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedEnrollmentNumber": {"$ref": "#/components/schemas/StudyV4.Range"}, "plannedSex": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "criteria": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.EligibilityCriterion"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyElement": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "transitionStartRule": {"$ref": "#/components/schemas/StudyV4.TransitionRule"}, "transitionEndRule": {"$ref": "#/components/schemas/StudyV4.TransitionRule"}, "studyInterventionId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyEpoch": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "nextId": {"type": "string", "nullable": true}, "previousId": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyIdentifier": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "studyIdentifier": {"type": "string", "nullable": true}, "studyIdentifierScope": {"$ref": "#/components/schemas/StudyV4.Organization"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyIntervention": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "codes": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "role": {"$ref": "#/components/schemas/StudyV4.Code"}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "minimumResponseDuration": {"$ref": "#/components/schemas/StudyV4.Quantity"}, "administrations": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.AgentAdministration"}, "nullable": true}, "productDesignation": {"$ref": "#/components/schemas/StudyV4.Code"}, "pharmacologicClass": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyProtocolDocument": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "versions": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyProtocolDocumentVersion"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyProtocolDocumentVersion": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "protocolStatus": {"$ref": "#/components/schemas/StudyV4.Code"}, "protocolVersion": {"type": "string", "nullable": true}, "dateValues": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.GovernanceDate"}, "nullable": true}, "contents": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.NarrativeContent"}, "nullable": true}, "childIds": {"type": "array", "items": {"type": "string"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudySite": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "currentEnrollment": {"$ref": "#/components/schemas/StudyV4.SubjectEnrollment"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyTitle": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.StudyVersion": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "titles": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyTitle"}, "nullable": true}, "versionIdentifier": {"type": "string", "nullable": true}, "studyType": {"$ref": "#/components/schemas/StudyV4.Code"}, "rationale": {"type": "string", "nullable": true}, "documentVersionId": {"type": "string", "nullable": true}, "dateValues": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.GovernanceDate"}, "nullable": true}, "amendments": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyAmendment"}, "nullable": true}, "studyIdentifiers": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyIdentifier"}, "nullable": true}, "studyPhase": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "businessTherapeuticAreas": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.Code"}, "nullable": true}, "studyDesigns": {"type": "array", "items": {"$ref": "#/components/schemas/StudyV4.StudyDesign"}, "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.SubjectEnrollment": {"type": "object", "properties": {"quantity": {"$ref": "#/components/schemas/StudyV4.Quantity"}, "id": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "code": {"$ref": "#/components/schemas/StudyV4.AliasCode"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.SyntaxTemplateDictionary": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "parameterMaps": {"nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.Timing": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "type": {"$ref": "#/components/schemas/StudyV4.Code"}, "value": {"type": "string", "nullable": true}, "valueLabel": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "windowLabel": {"type": "string", "nullable": true}, "relativeToScheduledInstanceId": {"type": "string", "nullable": true}, "relativeFromScheduledInstanceId": {"type": "string", "nullable": true}, "windowLower": {"type": "string", "nullable": true}, "windowUpper": {"type": "string", "nullable": true}, "relativeToFrom": {"$ref": "#/components/schemas/StudyV4.Code"}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "StudyV4.TransitionRule": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "name": {"type": "string", "nullable": true}, "label": {"type": "string", "nullable": true}, "description": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "instanceType": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Token.UserLogin": {"type": "object", "properties": {"username": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGroups.GroupFilter": {"type": "object", "properties": {"groupFieldName": {"type": "string", "nullable": true}, "groupFilterValues": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.GroupFilterValues"}, "nullable": true}}, "additionalProperties": false}, "UserGroups.GroupFilterValues": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "title": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGroups.GroupsTaggedToUser": {"type": "object", "properties": {"groupId": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}}, "additionalProperties": false}, "UserGroups.PostUserToGroups": {"type": "object", "properties": {"oid": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "groups": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.GroupsTaggedToUser"}, "nullable": true}}, "additionalProperties": false}, "UserGroups.SDRGroups": {"type": "object", "properties": {"groupId": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "groupDescription": {"type": "string", "nullable": true}, "permission": {"type": "string", "nullable": true}, "groupFilter": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.GroupFilter"}, "nullable": true}, "users": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.Users"}, "nullable": true}, "groupCreatedBy": {"type": "string", "nullable": true}, "groupCreatedOn": {"type": "string", "nullable": true}, "groupModifiedBy": {"type": "string", "nullable": true}, "groupModifiedOn": {"type": "string", "nullable": true}, "groupEnabled": {"type": "boolean"}}, "additionalProperties": false}, "UserGroups.UserGroupMapping": {"type": "object", "properties": {"sdrGroups": {"type": "array", "items": {"$ref": "#/components/schemas/UserGroups.SDRGroups"}, "nullable": true}}, "additionalProperties": false}, "UserGroups.UserGroupsQueryParameters": {"type": "object", "properties": {"pageNumber": {"type": "integer", "format": "int32"}, "pageSize": {"type": "integer", "format": "int32"}, "sortOrder": {"type": "string", "nullable": true}, "sortBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserGroups.Users": {"type": "object", "properties": {"oid": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "isActive": {"type": "boolean"}, "groupId": {"type": "string", "nullable": true}, "groupName": {"type": "string", "nullable": true}, "groupModifiedOn": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "Utilities.Common.ApiUsdmVersionMapping": {"type": "object", "properties": {"SDRVersions": {"type": "array", "items": {"$ref": "#/components/schemas/Utilities.Common.SDRVersions"}, "nullable": true}}, "additionalProperties": false}, "Utilities.Common.SDRVersions": {"type": "object", "properties": {"apiVersion": {"type": "string", "nullable": true}, "usdmVersions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}}, "securitySchemes": {"Bearer": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT Authorization header using the Bearer scheme (Example: 'Bearer 12345abcdef')", "name": "Authorization", "in": "header"}}}, "security": [{"Bearer": []}]}