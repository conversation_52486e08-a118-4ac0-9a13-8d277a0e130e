{"study": {"studyId": "", "studyTitle": "Safety and Efficacy of the Xanomeline Transdermal Therapeutic System (TTS) in Patients with Mild to Moderate Alzheimer\\’s Disease", "studyVersion": "1", "studyType": {"codeId": "Code_1", "code": "C98388", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Interventional Study"}, "studyRationale": "The discontinuation rate associated with this oral dosing regimen was 58.6% in previous studies, and alternative clinical strategies have been sought to improve tolerance for the compound. To that end, development of a Transdermal Therapeutic System (TTS) has been initiated.", "studyAcronym": "H2Q-MC-LZZT", "studyIdentifiers": [{"studyIdentifierId": "StudyIdentifier_1", "studyIdentifier": "H2Q-MC-LZZT", "studyIdentifierScope": {"organisationId": "Organisation_1", "organisationIdentifier": "00-642-1325", "organisationIdentifierScheme": "DUNS", "organisationName": "<PERSON>", "organisationType": {"codeId": "Code_5", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinical Study Sponsor"}, "organizationLegalAddress": null}}], "studyPhase": {"aliasCodeId": "AliasCode_1", "standardCode": {"codeId": "Code_2", "code": "C15601", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Phase II Trial"}, "standardCodeAliases": []}, "businessTherapeuticAreas": [{"codeId": "Code_3", "code": "PHARMA", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "<PERSON>"}], "studyProtocolVersions": [{"studyProtocolVersionId": "StudyProtocolVersion_1", "briefTitle": "Xanomeline (LY246708)", "officialTitle": "Safety and Efficacy of the Xanomeline\nTransdermal Therapeutic System (TTS) in Patients\nwith Mild to Moderate Alzheimer\\’s Disease", "protocolAmendment": "", "protocolEffectiveDate": "2006-01-01", "protocolStatus": {"codeId": "Code_4", "code": "C25508", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Final"}, "protocolVersion": "1", "publicTitle": "Safety and Efficacy of the Xanomeline Transdermal\nTherapeutic System (TTS) in Patients with Mild to\nModerate Alzheimer\\'s Disease", "scientificTitle": "Missing"}], "studyDesigns": [{"studyDesignId": "StudyDesign_1", "studyDesignName": "Study Design 1", "studyDesignDescription": "The main design for the study", "trialIntentTypes": [{"codeId": "Code_75", "code": "C49656", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Treatment Study"}], "trialType": [{"codeId": "Code_76", "code": "C49666", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Efficacy Study"}, {"codeId": "Code_77", "code": "C49667", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Safety Study"}, {"codeId": "Code_78", "code": "C49663", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Pharmacokinetic Study"}], "interventionModel": {"codeId": "Code_79", "code": "C82639", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Parallel Study"}, "studyCells": [{"studyCellId": "StudyCell_1", "studyArmId": "StudyArm_1", "studyEpochId": "StudyEpoch_1", "studyElementIds": ["StudyElement_1"]}, {"studyCellId": "StudyCell_2", "studyArmId": "StudyArm_1", "studyEpochId": "StudyEpoch_2", "studyElementIds": ["StudyElement_2"]}, {"studyCellId": "StudyCell_3", "studyArmId": "StudyArm_1", "studyEpochId": "StudyEpoch_3", "studyElementIds": ["StudyElement_2"]}, {"studyCellId": "StudyCell_4", "studyArmId": "StudyArm_1", "studyEpochId": "StudyEpoch_4", "studyElementIds": ["StudyElement_2"]}, {"studyCellId": "StudyCell_5", "studyArmId": "StudyArm_1", "studyEpochId": "StudyEpoch_5", "studyElementIds": ["StudyElement_7"]}, {"studyCellId": "StudyCell_6", "studyArmId": "StudyArm_2", "studyEpochId": "StudyEpoch_1", "studyElementIds": ["StudyElement_1"]}, {"studyCellId": "StudyCell_7", "studyArmId": "StudyArm_2", "studyEpochId": "StudyEpoch_2", "studyElementIds": ["StudyElement_3"]}, {"studyCellId": "StudyCell_8", "studyArmId": "StudyArm_2", "studyEpochId": "StudyEpoch_3", "studyElementIds": ["StudyElement_3"]}, {"studyCellId": "StudyCell_9", "studyArmId": "StudyArm_2", "studyEpochId": "StudyEpoch_4", "studyElementIds": ["StudyElement_3"]}, {"studyCellId": "StudyCell_10", "studyArmId": "StudyArm_2", "studyEpochId": "StudyEpoch_5", "studyElementIds": ["StudyElement_7"]}, {"studyCellId": "StudyCell_11", "studyArmId": "StudyArm_3", "studyEpochId": "StudyEpoch_1", "studyElementIds": ["StudyElement_1"]}, {"studyCellId": "StudyCell_12", "studyArmId": "StudyArm_3", "studyEpochId": "StudyEpoch_2", "studyElementIds": ["StudyElement_4"]}, {"studyCellId": "StudyCell_13", "studyArmId": "StudyArm_3", "studyEpochId": "StudyEpoch_3", "studyElementIds": ["StudyElement_5"]}, {"studyCellId": "StudyCell_14", "studyArmId": "StudyArm_3", "studyEpochId": "StudyEpoch_4", "studyElementIds": ["StudyElement_6"]}, {"studyCellId": "StudyCell_15", "studyArmId": "StudyArm_3", "studyEpochId": "StudyEpoch_5", "studyElementIds": ["StudyElement_7"]}], "studyIndications": [{"indicationId": "Indication_1", "indicationDescription": "Alzheimer's disease", "codes": [{"codeId": "Code_262", "code": "G30.9", "codeSystem": "ICD-10-CM", "codeSystemVersion": "Missing", "decode": "Alzheimer's disease; unspecified"}]}, {"indicationId": "Indication_2", "indicationDescription": "Alzheimer's disease", "codes": [{"codeId": "Code_263", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer's disease"}]}], "studyInvestigationalInterventions": [], "studyPopulations": [{"studyDesignPopulationId": "StudyDesignPopulation_1", "populationDescription": "Patients with Probable Mild to Moderate Alzheimer\\'s Disease", "plannedNumberOfParticipants": 300, "plannedMaximumAgeOfParticipants": "100 years", "plannedMinimumAgeOfParticipants": "50 years", "plannedSexOfParticipants": []}], "studyObjectives": [{"objectiveId": "Objective_1", "objectiveDescription": "To determine if there is a statistically significant relationship (overall Type 1 error\nrate, alpha=0.05) between the change in both the ADAS-Cog (11) and CIBIC+\nscores, and drug dose (0, 50 cm2 [54 mg], and 75 cm2 [81 mg]).", "objectiveLevel": {"codeId": "Code_266", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Trial Primary Objective"}, "objectiveEndpoints": [{"endpointId": "Endpoint_1", "endpointDescription": "Alzheimer\\'s Disease Assessment Scale - Cognitive Subscale, total of 11 items\n[ADAS-Cog (11)] at Week 24", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_265", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Primary Endpoint"}}, {"endpointId": "Endpoint_2", "endpointDescription": "Video-referenced Clinician’s Interview-based Impression of Change (CIBIC+) at\nWeek 24", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_267", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Primary Endpoint"}}]}, {"objectiveId": "Objective_2", "objectiveDescription": "To document the safety profile of the xanomeline TTS.", "objectiveLevel": {"codeId": "Code_269", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Trial Primary Objective"}, "objectiveEndpoints": [{"endpointId": "Endpoint_3", "endpointDescription": "Adverse events", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_268", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Primary Endpoint"}}, {"endpointId": "Endpoint_4", "endpointDescription": "Vital signs (weight, standing and supine blood pressure, heart rate)", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_270", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Primary Endpoint"}}, {"endpointId": "Endpoint_5", "endpointDescription": "Laboratory evaluations (Change from Baseline)", "endpointPurposeDescription": "The change from baseline laboratory value will be \ncalculated as the difference between the baseline lab value and the endpoint value (i.e.,\nthe value at the specified visit) or the end of treatment observation", "endpointLevel": {"codeId": "Code_271", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Primary Endpoint"}}]}, {"objectiveId": "Objective_3", "objectiveDescription": "To assess the dose-dependent improvement in behavior. Improved scores on the\nRevised Neuropsychiatric Inventory (NPI-X) will indicate improvement in these\nareas.", "objectiveLevel": {"codeId": "Code_273", "code": "C85827", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Trial Secondary Objective"}, "objectiveEndpoints": [{"endpointId": "Endpoint_6", "endpointDescription": "Alzheimer\\'s Disease Assessment Scale - Cognitive Subscale, total of 11 items\n[ADAS-Cog (11)] at Weeks 8 and 16", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_272", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Secondary Endpoint"}}, {"endpointId": "Endpoint_7", "endpointDescription": "Video-referenced Clinician\\’s Interview-based Impression of Change (CIBIC+) at\nWeeks 8 and 16", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_274", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Secondary Endpoint"}}, {"endpointId": "Endpoint_8", "endpointDescription": "Mean Revised Neuropsychiatric Inventory (NPI-X) from Week 4 to Week 24", "endpointPurposeDescription": "Missing", "endpointLevel": {"codeId": "Code_275", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Secondary Endpoint"}}]}], "studyScheduleTimelines": [{"scheduleTimelineId": "ScheduleTimeline_1", "scheduleTimelineName": "Main Timeline", "scheduleTimelineDescription": "This is the main timeline for the study design.", "entryCondition": "Potential subject identified", "scheduleTimelineEntryId": "ScheduledActivityInstance_1", "scheduleTimelineExits": [{"scheduleTimelineExitId": "ScheduleTimelineExit_1"}], "mainTimeline": true, "scheduleTimelineInstances": [{"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_1", "activityIds": ["Activity_1", "Activity_2", "Activity_3", "Activity_4", "Activity_5", "Activity_6", "Activity_7", "Activity_8", "Activity_9", "Activity_10", "Activity_13", "Activity_16", "Activity_17", "Activity_18", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_1", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_2", "epochId": "StudyEpoch_1", "scheduledInstanceTimings": [{"timingId": "Timing_1", "timingType": {"codeId": "Code_82", "code": "C99901x2", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Before"}, "timingValue": "PT0M", "timingDescription": null, "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_2", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_1", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_84", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_2", "activityIds": ["Activity_13", "Activity_14", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_2", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_3", "epochId": "StudyEpoch_1", "scheduledInstanceTimings": [{"timingId": "Timing_2", "timingType": {"codeId": "Code_87", "code": "C99901x2", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Before"}, "timingValue": "PT24H", "timingDescription": "24 Hours", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_2", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_89", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_3", "activityIds": ["Activity_12", "Activity_13", "Activity_15", "Activity_19", "Activity_23", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_3", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_4", "epochId": "StudyEpoch_2", "scheduledInstanceTimings": [{"timingId": "Timing_3", "timingType": {"codeId": "Code_90", "code": "C99901x3", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Fixed Reference"}, "timingValue": "PT0M", "timingDescription": null, "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_3", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_94", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_4", "activityIds": ["Activity_11", "Activity_13", "Activity_14"], "scheduledInstanceId": "ScheduledActivityInstance_4", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_5", "epochId": "StudyEpoch_2", "scheduledInstanceTimings": [{"timingId": "Timing_4", "timingType": {"codeId": "Code_96", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P13D", "timingDescription": "+13 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_4", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_99", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_5", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_5", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_6", "epochId": "StudyEpoch_2", "scheduledInstanceTimings": [{"timingId": "Timing_5", "timingType": {"codeId": "Code_101", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_4", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_5", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_104", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_6", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_6", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_7", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_6", "timingType": {"codeId": "Code_106", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P14D", "timingDescription": "+14 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_5", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_6", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_109", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_7", "activityIds": ["Activity_15"], "scheduledInstanceId": "ScheduledActivityInstance_7", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_8", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_7", "timingType": {"codeId": "Code_111", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_6", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_7", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_114", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_8", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_8", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_9", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_8", "timingType": {"codeId": "Code_116", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P13D", "timingDescription": "+13 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_7", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_8", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_119", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_9", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_9", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_10", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_9", "timingType": {"codeId": "Code_121", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P14D", "timingDescription": "+14 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_8", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_9", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_124", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_10", "activityIds": ["Activity_30"], "scheduledInstanceId": "ScheduledActivityInstance_10", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_11", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_10", "timingType": {"codeId": "Code_126", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_9", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_10", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_129", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_11", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_11", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_12", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_11", "timingType": {"codeId": "Code_131", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P27D", "timingDescription": "+27 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_10", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_11", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_134", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_12", "activityIds": ["Activity_30"], "scheduledInstanceId": "ScheduledActivityInstance_12", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_13", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_12", "timingType": {"codeId": "Code_136", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_12", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_139", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_13", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_13", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_14", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_13", "timingType": {"codeId": "Code_141", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P27D", "timingDescription": "+27 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_12", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_13", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_144", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_14", "activityIds": ["Activity_30"], "scheduledInstanceId": "ScheduledActivityInstance_14", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_15", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_14", "timingType": {"codeId": "Code_146", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_13", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_14", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_149", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_15", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_15", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_16", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_15", "timingType": {"codeId": "Code_151", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P27D", "timingDescription": "+27 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_14", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_15", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_154", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_16", "activityIds": ["Activity_30"], "scheduledInstanceId": "ScheduledActivityInstance_16", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_17", "epochId": "StudyEpoch_3", "scheduledInstanceTimings": [{"timingId": "Timing_16", "timingType": {"codeId": "Code_156", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P1D", "timingDescription": "+1 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_15", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_16", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_159", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_17", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_17", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": "ScheduledActivityInstance_18", "epochId": "StudyEpoch_4", "scheduledInstanceTimings": [{"timingId": "Timing_17", "timingType": {"codeId": "Code_161", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P27D", "timingDescription": "+27 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_16", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_17", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_164", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}, {"scheduledInstanceType": "ACTIVITY", "scheduledActivityInstanceEncounterId": "Encounter_18", "activityIds": ["Activity_7", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_26", "Activity_30", "Activity_31"], "scheduledInstanceId": "ScheduledActivityInstance_18", "scheduleTimelineExitId": null, "scheduledInstanceTimelineId": null, "defaultConditionId": null, "epochId": "StudyEpoch_5", "scheduledInstanceTimings": [{"timingId": "Timing_18", "timingType": {"codeId": "Code_166", "code": "C99901x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "After"}, "timingValue": "P14D", "timingDescription": "+14 Days", "timingWindow": null, "relativeToScheduledInstanceId": "ScheduledActivityInstance_17", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_18", "timingWindowLower": null, "timingWindowUpper": null, "timingRelativeToFrom": {"codeId": "Code_169", "code": "C99900x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Start to Start"}}]}]}], "therapeuticAreas": [{"codeId": "Code_72", "code": "MILD_MOD_ALZ", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Mild to Moderate Alzheimer\\'s Disease"}, {"codeId": "Code_73", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer\\'s disease"}], "studyEstimands": [], "encounters": [{"encounterId": "Encounter_1", "encounterContactModes": [{"codeId": "Code_9", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Screening encounter", "encounterEnvironmentalSetting": {"codeId": "Code_8", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Screening 1", "encounterType": {"codeId": "Code_7", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_2", "previousEncounterId": null, "encounterScheduledAtTimingId": null, "transitionStartRule": {"transitionRuleId": "TransitionRule_1", "transitionRuleDescription": "Subject identifier"}, "transitionEndRule": {"transitionRuleId": "TransitionRule_2", "transitionRuleDescription": "completion of screening activities"}}, {"encounterId": "Encounter_2", "encounterContactModes": [{"codeId": "Code_12", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Screening encounter - Ambulatory ECG Placement", "encounterEnvironmentalSetting": {"codeId": "Code_11", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Screening 2", "encounterType": {"codeId": "Code_10", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_3", "previousEncounterId": "Encounter_1", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": {"transitionRuleId": "TransitionRule_3", "transitionRuleDescription": "subject leaves clinic after connection of ambulatory ECG machine"}}, {"encounterId": "Encounter_3", "encounterContactModes": [{"codeId": "Code_15", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Baseline encounter - Ambulatory ECG Removal", "encounterEnvironmentalSetting": {"codeId": "Code_14", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Baseline", "encounterType": {"codeId": "Code_13", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_4", "previousEncounterId": "Encounter_2", "encounterScheduledAtTimingId": null, "transitionStartRule": {"transitionRuleId": "TransitionRule_4", "transitionRuleDescription": "subject has connection of ambulatory ECG machine removed"}, "transitionEndRule": {"transitionRuleId": "TransitionRule_5", "transitionRuleDescription": "Radomized"}}, {"encounterId": "Encounter_4", "encounterContactModes": [{"codeId": "Code_18", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 13 - Ambulatory ECG Placement", "encounterEnvironmentalSetting": {"codeId": "Code_17", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 1", "encounterType": {"codeId": "Code_16", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_5", "previousEncounterId": "Encounter_3", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_5", "encounterContactModes": [{"codeId": "Code_21", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 14", "encounterEnvironmentalSetting": {"codeId": "Code_20", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 2", "encounterType": {"codeId": "Code_19", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_6", "previousEncounterId": "Encounter_4", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_6", "encounterContactModes": [{"codeId": "Code_24", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 28", "encounterEnvironmentalSetting": {"codeId": "Code_23", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 4", "encounterType": {"codeId": "Code_22", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_7", "previousEncounterId": "Encounter_5", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_7", "encounterContactModes": [{"codeId": "Code_27", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 30 - Ambulatory ECG  Removal", "encounterEnvironmentalSetting": {"codeId": "Code_26", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 5", "encounterType": {"codeId": "Code_25", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_8", "previousEncounterId": "Encounter_6", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_8", "encounterContactModes": [{"codeId": "Code_30", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 42", "encounterEnvironmentalSetting": {"codeId": "Code_29", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 6", "encounterType": {"codeId": "Code_28", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_9", "previousEncounterId": "Encounter_7", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_9", "encounterContactModes": [{"codeId": "Code_33", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 56", "encounterEnvironmentalSetting": {"codeId": "Code_32", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 8", "encounterType": {"codeId": "Code_31", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_10", "previousEncounterId": "Encounter_8", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_10", "encounterContactModes": [{"codeId": "Code_36", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Telephone Call"}], "encounterDescription": "Day 70", "encounterEnvironmentalSetting": {"codeId": "Code_35", "code": "C18002", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Home"}, "encounterName": "Week 10 (T)", "encounterType": {"codeId": "Code_34", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_11", "previousEncounterId": "Encounter_9", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_11", "encounterContactModes": [{"codeId": "Code_39", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 84", "encounterEnvironmentalSetting": {"codeId": "Code_38", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 12", "encounterType": {"codeId": "Code_37", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_12", "previousEncounterId": "Encounter_10", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_12", "encounterContactModes": [{"codeId": "Code_42", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Telephone Call"}], "encounterDescription": "Day 98", "encounterEnvironmentalSetting": {"codeId": "Code_41", "code": "C18002", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Home"}, "encounterName": "Week 14 (T)", "encounterType": {"codeId": "Code_40", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_13", "previousEncounterId": "Encounter_11", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_13", "encounterContactModes": [{"codeId": "Code_45", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 112", "encounterEnvironmentalSetting": {"codeId": "Code_44", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 16", "encounterType": {"codeId": "Code_43", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_14", "previousEncounterId": "Encounter_12", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_14", "encounterContactModes": [{"codeId": "Code_48", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Telephone Call"}], "encounterDescription": "Day 126", "encounterEnvironmentalSetting": {"codeId": "Code_47", "code": "C18002", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Home"}, "encounterName": "Week 18 (T)", "encounterType": {"codeId": "Code_46", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_15", "previousEncounterId": "Encounter_13", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_15", "encounterContactModes": [{"codeId": "Code_51", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 140", "encounterEnvironmentalSetting": {"codeId": "Code_50", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 20", "encounterType": {"codeId": "Code_49", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_16", "previousEncounterId": "Encounter_14", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_16", "encounterContactModes": [{"codeId": "Code_54", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Telephone Call"}], "encounterDescription": "Day 154", "encounterEnvironmentalSetting": {"codeId": "Code_53", "code": "C18002", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Home"}, "encounterName": "Week 22 (T)", "encounterType": {"codeId": "Code_52", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_17", "previousEncounterId": "Encounter_15", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_17", "encounterContactModes": [{"codeId": "Code_57", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 168", "encounterEnvironmentalSetting": {"codeId": "Code_56", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 24", "encounterType": {"codeId": "Code_55", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": "Encounter_18", "previousEncounterId": "Encounter_16", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": null}, {"encounterId": "Encounter_18", "encounterContactModes": [{"codeId": "Code_60", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "In Person"}], "encounterDescription": "Day 182", "encounterEnvironmentalSetting": {"codeId": "Code_59", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinic"}, "encounterName": "Week 26", "encounterType": {"codeId": "Code_58", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Visit"}, "nextEncounterId": null, "previousEncounterId": "Encounter_17", "encounterScheduledAtTimingId": null, "transitionStartRule": null, "transitionEndRule": {"transitionRuleId": "TransitionRule_6", "transitionRuleDescription": "End of treatment"}}], "activities": [{"activityId": "Activity_1", "activityDescription": "Informed consent", "activityName": "Informed consent", "definedProcedures": [], "nextActivityId": "Activity_2", "previousActivityId": null, "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_2", "activityDescription": "Inclusion/exclusion criteria", "activityName": "Inclusion/exclusion criteria", "definedProcedures": [], "nextActivityId": "Activity_3", "previousActivityId": "Activity_1", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_3", "activityDescription": "Patient number assigned", "activityName": "Patient number assigned", "definedProcedures": [], "nextActivityId": "Activity_4", "previousActivityId": "Activity_2", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_4", "activityDescription": "Demographics", "activityName": "Demographics", "definedProcedures": [], "nextActivityId": "Activity_5", "previousActivityId": "Activity_3", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": ["BiomedicalConceptSurrogate_1"], "biomedicalConceptIds": ["BiomedicalConcept_1", "BiomedicalConcept_2"], "activityTimelineId": ""}, {"activityId": "Activity_5", "activityDescription": "<PERSON><PERSON><PERSON> <= 4", "activityName": "<PERSON><PERSON><PERSON> <= 4", "definedProcedures": [], "nextActivityId": "Activity_6", "previousActivityId": "Activity_4", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_6", "activityDescription": "MMSE 10-23", "activityName": "MMSE 10-23", "definedProcedures": [], "nextActivityId": "Activity_7", "previousActivityId": "Activity_5", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_7", "activityDescription": "Physical examination", "activityName": "Physical examination", "definedProcedures": [], "nextActivityId": "Activity_8", "previousActivityId": "Activity_6", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_8", "activityDescription": "Medical history", "activityName": "Medical history", "definedProcedures": [], "nextActivityId": "Activity_9", "previousActivityId": "Activity_7", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_9", "activityDescription": "Habits", "activityName": "Habits", "definedProcedures": [], "nextActivityId": "Activity_10", "previousActivityId": "Activity_8", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_10", "activityDescription": "Chest X-ray", "activityName": "Chest X-ray", "definedProcedures": [], "nextActivityId": "Activity_11", "previousActivityId": "Activity_9", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_11", "activityDescription": "Apo E genotyping", "activityName": "Apo E genotyping", "definedProcedures": [], "nextActivityId": "Activity_12", "previousActivityId": "Activity_10", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_12", "activityDescription": "Patient randomised", "activityName": "Patient randomised", "definedProcedures": [], "nextActivityId": "Activity_13", "previousActivityId": "Activity_11", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_13", "activityDescription": "Vital signs /Temperature", "activityName": "Vital signs /Temperature", "definedProcedures": [], "nextActivityId": "Activity_14", "previousActivityId": "Activity_12", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_3", "BiomedicalConcept_4", "BiomedicalConcept_5", "BiomedicalConcept_6", "BiomedicalConcept_7"], "activityTimelineId": ""}, {"activityId": "Activity_14", "activityDescription": "Ambulatory ECG placed", "activityName": "Ambulatory ECG placed", "definedProcedures": [], "nextActivityId": "Activity_15", "previousActivityId": "Activity_13", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_15", "activityDescription": "Ambulatory ECG removed", "activityName": "Ambulatory ECG removed", "definedProcedures": [], "nextActivityId": "Activity_16", "previousActivityId": "Activity_14", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_16", "activityDescription": "ECG", "activityName": "ECG", "definedProcedures": [], "nextActivityId": "Activity_17", "previousActivityId": "Activity_15", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_17", "activityDescription": "Placebo TTS test", "activityName": "Placebo TTS test", "definedProcedures": [], "nextActivityId": "Activity_18", "previousActivityId": "Activity_16", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_18", "activityDescription": "CT scan", "activityName": "CT scan", "definedProcedures": [], "nextActivityId": "Activity_19", "previousActivityId": "Activity_17", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_19", "activityDescription": "Concomitant medications", "activityName": "Concomitant medications", "definedProcedures": [], "nextActivityId": "Activity_20", "previousActivityId": "Activity_18", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_20", "activityDescription": "Hematology", "activityName": "Hematology", "definedProcedures": [], "nextActivityId": "Activity_21", "previousActivityId": "Activity_19", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_21", "activityDescription": "Chemistry", "activityName": "Chemistry", "definedProcedures": [], "nextActivityId": "Activity_22", "previousActivityId": "Activity_20", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_8", "BiomedicalConcept_9", "BiomedicalConcept_10", "BiomedicalConcept_11", "BiomedicalConcept_12", "BiomedicalConcept_13", "BiomedicalConcept_14"], "activityTimelineId": ""}, {"activityId": "Activity_22", "activityDescription": "Uninalysis", "activityName": "Uninalysis", "definedProcedures": [], "nextActivityId": "Activity_23", "previousActivityId": "Activity_21", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_23", "activityDescription": "Plasma Specimen\n(Xanomeline)", "activityName": "Plasma Specimen\n(Xanomeline)", "definedProcedures": [], "nextActivityId": "Activity_24", "previousActivityId": "Activity_22", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_24", "activityDescription": "Hemoglobin A1C", "activityName": "Hemoglobin A1C", "definedProcedures": [], "nextActivityId": "Activity_25", "previousActivityId": "Activity_23", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": ["BiomedicalConceptSurrogate_2"], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_25", "activityDescription": "Study drug record\nMedications dispensed\nMedications returned", "activityName": "Study drug record\nMedications dispensed\nMedications returned", "definedProcedures": [], "nextActivityId": "Activity_26", "previousActivityId": "Activity_24", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_26", "activityDescription": "TTS Acceptability Survey", "activityName": "TTS Acceptability Survey", "definedProcedures": [], "nextActivityId": "Activity_27", "previousActivityId": "Activity_25", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_27", "activityDescription": "ADAS-Cog", "activityName": "ADAS-Cog", "definedProcedures": [], "nextActivityId": "Activity_28", "previousActivityId": "Activity_26", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_28", "activityDescription": "CIBIC+", "activityName": "CIBIC+", "definedProcedures": [], "nextActivityId": "Activity_29", "previousActivityId": "Activity_27", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_29", "activityDescription": "DAD", "activityName": "DAD", "definedProcedures": [], "nextActivityId": "Activity_30", "previousActivityId": "Activity_28", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_30", "activityDescription": "NPI-X", "activityName": "NPI-X", "definedProcedures": [], "nextActivityId": "Activity_31", "previousActivityId": "Activity_29", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "activityTimelineId": ""}, {"activityId": "Activity_31", "activityDescription": "Adverse events", "activityName": "Adverse events", "definedProcedures": [], "nextActivityId": null, "previousActivityId": "Activity_30", "activityIsConditional": false, "activityIsConditionalReason": "", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_15"], "activityTimelineId": ""}], "studyDesignRationale": "The discontinuation rate associated with this oral dosing regimen was 58.6% in previous studies, and alternative clinical strategies have been sought to improve tolerance for the compound. To that end, development of a Transdermal Therapeutic System (TTS) has been initiated.", "studyDesignBlindingScheme": {"aliasCodeId": "AliasCode_2", "standardCode": {"codeId": "Code_74", "code": "C15228", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Double Blind Study"}, "standardCodeAliases": []}, "biomedicalConcepts": [{"biomedicalConceptId": "BiomedicalConcept_1", "bcName": "Sex", "bcSynonyms": [], "bcReference": "/mdr/bc/packages/2023-03-31/biomedicalconcepts/C28421", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_1", "bcPropertyName": "Sex", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_1", "responseCodeEnabled": true, "code": {"codeId": "Code_171", "code": "C20197", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Male"}}, {"responseCodeId": "ResponseCode_2", "responseCodeEnabled": true, "code": {"codeId": "Code_172", "code": "C16576", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Female"}}, {"responseCodeId": "ResponseCode_3", "responseCodeEnabled": true, "code": {"codeId": "Code_173", "code": "C17998", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Unknown"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_4", "standardCode": {"codeId": "Code_174", "code": "C28421", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Sex"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_3", "standardCode": {"codeId": "Code_170", "code": "C28421", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Sex"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_2", "bcName": "Race", "bcSynonyms": ["Racial Group"], "bcReference": "/mdr/bc/packages/2023-03-31/biomedicalconcepts/C17049", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_2", "bcPropertyName": "Race", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_4", "responseCodeEnabled": true, "code": {"codeId": "Code_176", "code": "C17998", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Unknown"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_6", "standardCode": {"codeId": "Code_177", "code": "C17049", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Race"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_5", "standardCode": {"codeId": "Code_175", "code": "C17049", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Race"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_3", "bcName": "Systolic Blood Pressure", "bcSynonyms": ["SYSBP"], "bcReference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C25298", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_3", "bcPropertyName": "Vital Signs Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "integer", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_8", "standardCode": {"codeId": "Code_179", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_4", "bcPropertyName": "Unit of Pressure", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_5", "responseCodeEnabled": true, "code": {"codeId": "Code_180", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "<PERSON>"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_9", "standardCode": {"codeId": "Code_181", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Pressure"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_5", "bcPropertyName": "Vital Signs Location", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_10", "standardCode": {"codeId": "Code_182", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Location"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_6", "bcPropertyName": "Vital Signs Laterality", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_11", "standardCode": {"codeId": "Code_183", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Laterality"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_7", "bcPropertyName": "Test Method", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_12", "standardCode": {"codeId": "Code_184", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Test Method"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_8", "bcPropertyName": "Vital Signs Position", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_13", "standardCode": {"codeId": "Code_185", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Position"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_7", "standardCode": {"codeId": "Code_178", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Systolic Blood Pressure"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_4", "bcName": "Diastolic Blood Pressure", "bcSynonyms": ["DIABP"], "bcReference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C25299", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_9", "bcPropertyName": "Vital Signs Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "integer", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_15", "standardCode": {"codeId": "Code_187", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_10", "bcPropertyName": "Unit of Pressure", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_6", "responseCodeEnabled": true, "code": {"codeId": "Code_188", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "<PERSON>"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_16", "standardCode": {"codeId": "Code_189", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Pressure"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_11", "bcPropertyName": "Vital Signs Location", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_17", "standardCode": {"codeId": "Code_190", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Location"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_12", "bcPropertyName": "Vital Signs Laterality", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_18", "standardCode": {"codeId": "Code_191", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Laterality"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_13", "bcPropertyName": "Test Method", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_19", "standardCode": {"codeId": "Code_192", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Test Method"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_14", "bcPropertyName": "Vital Signs Position", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_20", "standardCode": {"codeId": "Code_193", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Position"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_14", "standardCode": {"codeId": "Code_186", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Diastolic Blood Pressure"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_5", "bcName": "Body Temperature", "bcSynonyms": ["Temperature"], "bcReference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C174446", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_15", "bcPropertyName": "Vital Signs Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_22", "standardCode": {"codeId": "Code_195", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_16", "bcPropertyName": "Unit of Temperature", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_7", "responseCodeEnabled": true, "code": {"codeId": "Code_196", "code": "C42537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "<PERSON><PERSON>"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_23", "standardCode": {"codeId": "Code_197", "code": "C44276", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Temperature"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_17", "bcPropertyName": "Vital Signs Location", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_24", "standardCode": {"codeId": "Code_198", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Location"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_21", "standardCode": {"codeId": "Code_194", "code": "C174446", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Body Temperature"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_6", "bcName": "Body Weight", "bcSynonyms": ["Weight"], "bcReference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C81328", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_18", "bcPropertyName": "Vital Signs Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_26", "standardCode": {"codeId": "Code_200", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_19", "bcPropertyName": "Unit of Weight", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_8", "responseCodeEnabled": true, "code": {"codeId": "Code_201", "code": "C28252", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Kilogram"}}, {"responseCodeId": "ResponseCode_9", "responseCodeEnabled": true, "code": {"codeId": "Code_202", "code": "C48155", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Gram"}}, {"responseCodeId": "ResponseCode_10", "responseCodeEnabled": true, "code": {"codeId": "Code_203", "code": "C48531", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Pound"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_27", "standardCode": {"codeId": "Code_204", "code": "C48208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Weight"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_25", "standardCode": {"codeId": "Code_199", "code": "C81328", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Body Weight"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_7", "bcName": "Body Height", "bcSynonyms": ["Height"], "bcReference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C164634", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_20", "bcPropertyName": "Vital Signs Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_29", "standardCode": {"codeId": "Code_206", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Vital Signs Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_21", "bcPropertyName": "Unit of Height", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_11", "responseCodeEnabled": true, "code": {"codeId": "Code_207", "code": "C49668", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Centimeter"}}, {"responseCodeId": "ResponseCode_12", "responseCodeEnabled": true, "code": {"codeId": "Code_208", "code": "C48500", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Inch"}}, {"responseCodeId": "ResponseCode_13", "responseCodeEnabled": true, "code": {"codeId": "Code_209", "code": "C28251", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Millimeter"}}, {"responseCodeId": "ResponseCode_14", "responseCodeEnabled": true, "code": {"codeId": "Code_210", "code": "C41139", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "<PERSON>er"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_30", "standardCode": {"codeId": "Code_211", "code": "C168688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Height"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_28", "standardCode": {"codeId": "Code_205", "code": "C164634", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Body Height"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_8", "bcName": "Alanine Aminotransferase Measurement", "bcSynonyms": ["ALT", "SGPT"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64433", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_22", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_32", "standardCode": {"codeId": "Code_213", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_23", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_33", "standardCode": {"codeId": "Code_214", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_24", "bcPropertyName": "Unit of Catalytic Activity Concentration", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_34", "standardCode": {"codeId": "Code_215", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Catalytic Activity Concentration"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_25", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_35", "standardCode": {"codeId": "Code_216", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_31", "standardCode": {"codeId": "Code_212", "code": "C64433", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Alanine Aminotransferase Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_9", "bcName": "Albumin Measurement", "bcSynonyms": ["Albumin"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64431", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_26", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_37", "standardCode": {"codeId": "Code_218", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_27", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_38", "standardCode": {"codeId": "Code_219", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_28", "bcPropertyName": "Unit of Mass or Substance Concentration", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_39", "standardCode": {"codeId": "Code_220", "code": "NEW_2", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Mass or Substance Concentration"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_29", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_40", "standardCode": {"codeId": "Code_221", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_36", "standardCode": {"codeId": "Code_217", "code": "C64431", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Albumin Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_10", "bcName": "Alkaline Phosphatase Measurement", "bcSynonyms": ["Alkaline Phosphatase"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64432", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_30", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_42", "standardCode": {"codeId": "Code_223", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_31", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_43", "standardCode": {"codeId": "Code_224", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_32", "bcPropertyName": "Unit of Catalytic Activity Concentration", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_44", "standardCode": {"codeId": "Code_225", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Catalytic Activity Concentration"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_33", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_45", "standardCode": {"codeId": "Code_226", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_41", "standardCode": {"codeId": "Code_222", "code": "C64432", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Alkaline Phosphatase Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_11", "bcName": "Aspartate Aminotransferase Measurement", "bcSynonyms": ["AST", "SGOT"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64467", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_34", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_47", "standardCode": {"codeId": "Code_228", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_35", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_48", "standardCode": {"codeId": "Code_229", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_36", "bcPropertyName": "Unit of Catalytic Activity Concentration", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_49", "standardCode": {"codeId": "Code_230", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Catalytic Activity Concentration"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_37", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_50", "standardCode": {"codeId": "Code_231", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_46", "standardCode": {"codeId": "Code_227", "code": "C64467", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Aspartate Aminotransferase Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_12", "bcName": "Creatinine Measurement", "bcSynonyms": ["Creatinine"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64547", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_38", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_52", "standardCode": {"codeId": "Code_233", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_39", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_53", "standardCode": {"codeId": "Code_234", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_40", "bcPropertyName": "Unit of Mass or Substance Concentration", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_54", "standardCode": {"codeId": "Code_235", "code": "NEW_2", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Unit of Mass or Substance Concentration"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_41", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_55", "standardCode": {"codeId": "Code_236", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_51", "standardCode": {"codeId": "Code_232", "code": "C64547", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Creatinine Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_13", "bcName": "Potassium Measurement", "bcSynonyms": ["Potassium", "K"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64853", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_42", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_57", "standardCode": {"codeId": "Code_238", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_43", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_58", "standardCode": {"codeId": "Code_239", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_44", "bcPropertyName": "Molarity Unit", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_59", "standardCode": {"codeId": "Code_240", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Molarity Unit"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_45", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_60", "standardCode": {"codeId": "Code_241", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_56", "standardCode": {"codeId": "Code_237", "code": "C64853", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Potassium Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_14", "bcName": "Sodium Measurement", "bcSynonyms": ["Sodium", "NA"], "bcReference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64809", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_46", "bcPropertyName": "Laboratory Test Result", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "decimal", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_62", "standardCode": {"codeId": "Code_243", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Result"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_47", "bcPropertyName": "Laboratory Test Fasting Status", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_63", "standardCode": {"codeId": "Code_244", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Laboratory Test Fasting Status"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_48", "bcPropertyName": "Molarity Unit", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_64", "standardCode": {"codeId": "Code_245", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Molarity Unit"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_49", "bcPropertyName": "Biospecimen Type", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_65", "standardCode": {"codeId": "Code_246", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Biospecimen Type"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_61", "standardCode": {"codeId": "Code_242", "code": "C64809", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Sodium Measurement"}, "standardCodeAliases": []}}, {"biomedicalConceptId": "BiomedicalConcept_15", "bcName": "Adverse Event", "bcSynonyms": ["Adverse Event Reported Term", "AETERM"], "bcReference": "/mdr/bc/packages/2023-03-31/biomedicalconcepts/C41331", "bcProperties": [{"bcPropertyId": "BiomedicalConceptProperty_50", "bcPropertyName": "Adverse Event Verbatim Description", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_67", "standardCode": {"codeId": "Code_248", "code": "C78541", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Verbatim Description"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_51", "bcPropertyName": "Adverse Event Dictionary Derived Term", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_68", "standardCode": {"codeId": "Code_249", "code": "C83344", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Dictionary Derived Term"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_52", "bcPropertyName": "Adverse Event Category", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_69", "standardCode": {"codeId": "Code_250", "code": "C83198", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Category"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_53", "bcPropertyName": "Adverse Event Subcategory", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_70", "standardCode": {"codeId": "Code_251", "code": "C83212", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Subcategory"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_54", "bcPropertyName": "Adverse Event Pre-specified", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_71", "standardCode": {"codeId": "Code_252", "code": "C87840", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Pre-specified"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_55", "bcPropertyName": "Severity of Adverse Event", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_72", "standardCode": {"codeId": "Code_253", "code": "C53253", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Severity of Adverse Event"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_56", "bcPropertyName": "Adverse Event Toxicity Grade", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_73", "standardCode": {"codeId": "Code_254", "code": "C78605", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Toxicity Grade"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_57", "bcPropertyName": "Seriousness of Adverse Event", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "boolean", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_74", "standardCode": {"codeId": "Code_255", "code": "C53252", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Seriousness of Adverse Event"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_58", "bcPropertyName": "Adverse Event Action Taken with Study Treatment", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_15", "responseCodeEnabled": true, "code": {"codeId": "Code_256", "code": "C17998", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Unknown"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_75", "standardCode": {"codeId": "Code_257", "code": "C83013", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Action Taken with Study Treatment"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_59", "bcPropertyName": "Adverse Event Attribution to Product or Procedure", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_76", "standardCode": {"codeId": "Code_258", "code": "C41358", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Attribution to Product or Procedure"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_60", "bcPropertyName": "Adverse Event Pattern", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_77", "standardCode": {"codeId": "Code_259", "code": "C83208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Pattern"}, "standardCodeAliases": []}}, {"bcPropertyId": "BiomedicalConceptProperty_61", "bcPropertyName": "Adverse Event Outcome", "bcPropertyRequired": true, "bcPropertyEnabled": true, "bcPropertyDataType": "string", "bcPropertyResponseCodes": [{"responseCodeId": "ResponseCode_16", "responseCodeEnabled": true, "code": {"codeId": "Code_260", "code": "C17998", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Unknown"}}], "bcPropertyConceptCode": {"aliasCodeId": "AliasCode_78", "standardCode": {"codeId": "Code_261", "code": "C49489", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event Outcome"}, "standardCodeAliases": []}}], "bcConceptCode": {"aliasCodeId": "AliasCode_66", "standardCode": {"codeId": "Code_247", "code": "C41331", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "Missing", "decode": "Adverse Event"}, "standardCodeAliases": []}}], "bcCategories": [], "bcSurrogates": [{"bcSurrogateId": "BiomedicalConceptSurrogate_1", "bcSurrogateName": "Date of Birth", "bcSurrogateDescription": "Date of Birth", "bcSurrogateReference": "Missing"}, {"bcSurrogateId": "BiomedicalConceptSurrogate_2", "bcSurrogateName": "HbA1c", "bcSurrogateDescription": "HbA1c", "bcSurrogateReference": "Missing"}], "studyArms": [{"studyArmId": "StudyArm_1", "studyArmDataOriginDescription": "Data collected from subjects", "studyArmDataOriginType": {"codeId": "Code_62", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Data Generated Within Study"}, "studyArmDescription": "Placebo", "studyArmName": "Placebo", "studyArmType": {"codeId": "Code_61", "code": "C174268", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Placebo Comparator Arm"}}, {"studyArmId": "StudyArm_2", "studyArmDataOriginDescription": "Data collected from subjects", "studyArmDataOriginType": {"codeId": "Code_64", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Data Generated Within Study"}, "studyArmDescription": "Active Substance", "studyArmName": "Xanomeline <PERSON>", "studyArmType": {"codeId": "Code_63", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Active Comparator Arm"}}, {"studyArmId": "StudyArm_3", "studyArmDataOriginDescription": "Data collected from subjects", "studyArmDataOriginType": {"codeId": "Code_66", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Data Generated Within Study"}, "studyArmDescription": "Active Substance", "studyArmName": "Xanomeline High Dose", "studyArmType": {"codeId": "Code_65", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Active Comparator Arm"}}], "studyEpochs": [{"studyEpochId": "StudyEpoch_1", "nextStudyEpochId": "StudyEpoch_2", "previousStudyEpochId": null, "studyEpochDescription": "Screening Epoch", "studyEpochName": "Screening", "studyEpochType": {"codeId": "Code_67", "code": "C48262", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Trial Screening"}}, {"studyEpochId": "StudyEpoch_2", "nextStudyEpochId": "StudyEpoch_3", "previousStudyEpochId": "StudyEpoch_1", "studyEpochDescription": "Treatment Epoch", "studyEpochName": "Treatment 1", "studyEpochType": {"codeId": "Code_68", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Treatment Epoch"}}, {"studyEpochId": "StudyEpoch_3", "nextStudyEpochId": "StudyEpoch_4", "previousStudyEpochId": "StudyEpoch_2", "studyEpochDescription": "Treatment Epoch", "studyEpochName": "Treatment 2", "studyEpochType": {"codeId": "Code_69", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Treatment Epoch"}}, {"studyEpochId": "StudyEpoch_4", "nextStudyEpochId": "StudyEpoch_5", "previousStudyEpochId": "StudyEpoch_3", "studyEpochDescription": "Treatment Epoch", "studyEpochName": "Treatment 3", "studyEpochType": {"codeId": "Code_70", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Treatment Epoch"}}, {"studyEpochId": "StudyEpoch_5", "nextStudyEpochId": null, "previousStudyEpochId": "StudyEpoch_4", "studyEpochDescription": "Follow-up Epoch", "studyEpochName": "Follow-Up", "studyEpochType": {"codeId": "Code_71", "code": "C99158", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-12-16", "decode": "Clinical Study Follow-up"}}], "studyElements": [{"studyElementId": "StudyElement_1", "studyElementDescription": "Screening Element", "studyElementName": "Screening", "transitionStartRule": {"transitionRuleId": "TransitionRule_7", "transitionRuleDescription": "Informed consent"}, "transitionEndRule": {"transitionRuleId": "TransitionRule_8", "transitionRuleDescription": "Completion of all screening activities and no more than 2 weeks from informed consent"}}, {"studyElementId": "StudyElement_2", "studyElementDescription": "Placebo TTS (adhesive patches)", "studyElementName": "Placebo", "transitionStartRule": {"transitionRuleId": "TransitionRule_9", "transitionRuleDescription": "Administration of first dose"}, "transitionEndRule": null}, {"studyElementId": "StudyElement_7", "studyElementDescription": "Follow Up Element", "studyElementName": "Follow_up", "transitionStartRule": {"transitionRuleId": "TransitionRule_14", "transitionRuleDescription": "End of last scheduled visit on study (including early termination)"}, "transitionEndRule": {"transitionRuleId": "TransitionRule_15", "transitionRuleDescription": "Completion of all specified followup activities (which vary on a patient-by-patient basis)"}}, {"studyElementId": "StudyElement_3", "studyElementDescription": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "studyElementName": "Low", "transitionStartRule": {"transitionRuleId": "TransitionRule_10", "transitionRuleDescription": "Administration of first dose"}, "transitionEndRule": null}, {"studyElementId": "StudyElement_4", "studyElementDescription": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "studyElementName": "High_Start", "transitionStartRule": {"transitionRuleId": "TransitionRule_11", "transitionRuleDescription": "Randomized"}, "transitionEndRule": null}, {"studyElementId": "StudyElement_5", "studyElementDescription": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg + 25 cm2, 27 mg", "studyElementName": "High_Middle", "transitionStartRule": {"transitionRuleId": "TransitionRule_12", "transitionRuleDescription": "Administration of first dose (from patches supplied at Visit 4)"}, "transitionEndRule": null}, {"studyElementId": "StudyElement_6", "studyElementDescription": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "studyElementName": "High_End", "transitionStartRule": {"transitionRuleId": "TransitionRule_13", "transitionRuleDescription": "Administration of first dose (from patches supplied at Visit 12)"}, "transitionEndRule": null}]}]}}