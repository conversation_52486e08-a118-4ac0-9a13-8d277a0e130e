{"studyId": "14840d80-83b0-4956-85e2-31257f5c12e7", "studyTitle": "Study of Stomach Cancer", "studyDesigns": [{"studyDesignId": "SD01", "studyDesignName": "Design for Lung Cancer", "studyDesignDescription": "Stage III Lung Cancer", "studyScheduleTimelines": [{"scheduleTimelineId": "Timeline01", "scheduleTimelineName": "name", "scheduleTimelineDescription": "Timeline for Stage III", "entryCondition": "condition", "scheduleTimelineSoA": {"orderOfActivities": [{"activityId": "ACT001", "activityDescription": "Informed consents", "activityName": "Informed consent", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "Reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT002", "activityDescription": "Informed consent", "activityName": "Eligibility criteria", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT003", "activityDescription": "Informed consent", "activityName": "Demography", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT005", "activityDescription": "Informed consent", "activityName": "Disease characteristics", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT006", "activityDescription": "Informed consent", "activityName": "Physical exam", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT007", "activityDescription": "Informed consent", "activityName": "Height", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT008", "activityDescription": "Informed consent", "activityName": "12-lead ECG", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT009", "activityDescription": "Informed consent", "activityName": "Hematology (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT010", "activityDescription": "Informed consent", "activityName": "Chemistry (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT011", "activityDescription": "Informed consent", "activityName": "Serology", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT012", "activityDescription": "Informed consent", "activityName": "Urinalysis", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT013", "activityDescription": "Informed consent", "activityName": "Pregnancy test", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT014", "activityDescription": "Informed consent", "activityName": "Ensure availability of medication X", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT015", "activityDescription": "Informed consent", "activityName": "Hospitalization", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT016", "activityDescription": "Informed consent", "activityName": "Weight", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT017", "activityDescription": "Informed consent", "activityName": "Vital signs", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT018", "activityDescription": "Informed consent", "activityName": "adverse events", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT019", "activityDescription": "Informed consent", "activityName": "Concomitant medications", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "Name", "procedureDescription": "Desc", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": "Name : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}], "SoA": [{"encounterId": "VIS11", "encounterName": "SCREENING VISIT", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}]}, {"encounterId": "VIS12", "encounterName": "RUN-IN VISIT 1", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT002"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT003", "ACT002"]}]}, {"encounterId": "VIS13", "encounterName": "RUN-IN VISIT 2", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 10", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT006"]}]}, {"encounterId": "VIS14", "encounterName": "RUN-IN VISIT 3", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 18", "timingWindow": "window", "timingType": "After", "activities": ["ACT006", "ACT007"]}]}, {"encounterId": "VIS15", "encounterName": "CYCLE 1, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 20", "timingWindow": "window", "timingType": "After", "activities": ["ACT008", "ACT009"]}]}, {"encounterId": "VIS16", "encounterName": "CYCLE 1, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 10", "timings": [{"timingValue": "Day 23", "timingWindow": "window", "timingType": "After", "activities": ["ACT010", "ACT011"]}]}, {"encounterId": "VIS17", "encounterName": "CYCLE 1, TREATMENT 3", "encounterScheduledAtTimingValue": "Day 18", "timings": [{"timingValue": "Day 25", "timingWindow": "window", "timingType": "After", "activities": ["ACT012", "ACT013"]}]}, {"encounterId": "VIS18", "encounterName": "CYCLE 2, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 20", "timings": [{"timingValue": "Day 30", "timingWindow": "window", "timingType": "After", "activities": ["ACT013", "ACT014"]}]}, {"encounterId": "VIS19", "encounterName": "CYCLE 2, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 23", "timings": [{"timingValue": "Day 35", "timingWindow": "window", "timingType": "After", "activities": ["ACT015", "ACT016"]}]}, {"encounterId": "VIS20", "encounterName": "CYCLE 3, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 25", "timings": [{"timingValue": "WEEK 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT017", "ACT018"]}]}, {"encounterId": "VIS21", "encounterName": "CYCLE 3, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 30", "timings": [{"timingValue": "WEEK 3", "timingWindow": "window", "timingType": "After", "activities": ["ACT018", "ACT019"]}]}, {"encounterId": "VIS22", "encounterName": "FU 1", "encounterScheduledAtTimingValue": "WEEK 6", "timings": [{"timingValue": "WEEK 5", "timingWindow": "window", "timingType": "After", "activities": ["ACT019", "ACT002"]}]}, {"encounterId": "VIS23", "encounterName": "FU 2", "encounterScheduledAtTimingValue": "MONTH 1", "timings": [{"timingValue": "WEEK 6", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT001"]}, {"timingValue": "MONTH 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT002"]}]}]}}]}]}