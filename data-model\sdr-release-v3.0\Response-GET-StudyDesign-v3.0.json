{"studyDesigns": [{"id": "StudyDesign_1", "name": "Study Design 1", "label": "", "description": "The main design for the study", "trialIntentTypes": [{"id": "Code_136", "code": "C49656", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Study", "instanceType": "Code"}], "trialTypes": [{"id": "Code_137", "code": "C49666", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Efficacy Study", "instanceType": "Code"}, {"id": "Code_138", "code": "C49667", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Safety Study", "instanceType": "Code"}, {"id": "Code_139", "code": "C49663", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pharmacokinetic Study", "instanceType": "Code"}], "characteristics": [{"id": "Code_142", "code": "C99907x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "EXTENSION", "instanceType": "Code"}, {"id": "Code_143", "code": "C98704", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "ADAPTIVE", "instanceType": "Code"}], "interventionModel": {"id": "Code_140", "code": "C82639", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Parallel Study", "instanceType": "Code"}, "studyCells": [{"id": "StudyCell_1", "armId": "StudyArm_1", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_2", "armId": "StudyArm_1", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_3", "armId": "StudyArm_1", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_4", "armId": "StudyArm_1", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_5", "armId": "StudyArm_1", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}, {"id": "StudyCell_6", "armId": "StudyArm_2", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_7", "armId": "StudyArm_2", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_8", "armId": "StudyArm_2", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_9", "armId": "StudyArm_2", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_10", "armId": "StudyArm_2", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}, {"id": "StudyCell_11", "armId": "StudyArm_3", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_12", "armId": "StudyArm_3", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_4"], "instanceType": "StudyCell"}, {"id": "StudyCell_13", "armId": "StudyArm_3", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_5"], "instanceType": "StudyCell"}, {"id": "StudyCell_14", "armId": "StudyArm_3", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_6"], "instanceType": "StudyCell"}, {"id": "StudyCell_15", "armId": "StudyArm_3", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}], "indications": [{"id": "Indication_1", "name": "IND1", "label": "Alzheimer's disease", "description": "Alzheimer's disease", "codes": [{"id": "Code_330", "code": "G30.9", "codeSystem": "ICD-10-CM", "codeSystemVersion": "1", "decode": "Alzheimer's disease; unspecified", "instanceType": "Code"}], "instanceType": "Indication"}, {"id": "Indication_2", "name": "IND2", "label": "Alzheimer's disease", "description": "Alzheimer's disease", "codes": [{"id": "Code_331", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer's disease", "instanceType": "Code"}], "instanceType": "Indication"}], "studyInterventions": [{"id": "interventionId", "name": "interventionName", "label": "label", "description": "interventionDesc", "codes": [{"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "BAD STUFF", "instanceType": "Code"}], "role": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Role", "instanceType": "Code"}, "type": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "type", "instanceType": "Code"}, "minimumResponseDuration": {"id": "quantityId", "unit": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "value": 25, "instanceType": "Quantity"}, "administrations": [{"id": "adminId", "name": "adminname", "label": "label", "description": "adminDesc", "duration": {"id": "durationId", "quantity": null, "description": "desc", "durationWillVary": true, "reasonDurationWillVary": "reason", "instanceType": "AdministrationDuration"}, "dose": {"id": "doseId", "unit": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "value": 10, "instanceType": "Quantity"}, "route": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "route", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "frequency": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "route", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "AgentAdministration"}], "productDesignation": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Designation", "instanceType": "Code"}, "pharmacologicClass": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "<PERSON><PERSON>", "instanceType": "Code"}, "instanceType": "StudyIntervention"}], "population": {"cohorts": [{"characteristics": [{"id": "characteristic_1", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": null, "instanceType": "Characteristic"}], "id": "Cohorts_1", "name": "POP1", "label": "", "description": "Patients with Probable Mild to Moderate Alzheimer's Disease", "includesHealthySubjects": false, "plannedAge": {"id": "Range_3", "minValue": 50, "maxValue": 100, "unit": {"id": "Code_332", "code": "C29848", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Year", "instanceType": "Code"}, "isApproximate": false, "instanceType": "Range"}, "plannedCompletionNumber": {"id": "Range_1", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedEnrollmentNumber": {"id": "Range_2", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedSex": [{"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f614", "code": "C16576", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}, {"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f616", "code": "C20197", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}], "criteria": [{"category": {"id": "Code_345", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "1", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_1", "name": "Age Criteria", "label": "", "description": "The study age criterion", "text": "Subjects shall be between [min_age] and [max_age]", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_346", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "2", "previousId": "EligibilityCriterion_1", "nextId": null, "contextId": "StudyVersion_1", "id": "EligibilityCriterion_2", "name": "Pop Criteria", "label": "", "description": "The study population criterion", "text": "[StudyPopulation] as defined by the NINCDS and the ADRDA guidelines (Attachment LZZT.7)", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_347", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "3", "previousId": null, "nextId": "EligibilityCriterion_2", "contextId": null, "id": "EligibilityCriterion_3", "name": "Diag Criteria", "label": "", "description": "The study diagnosis criterion", "text": "[Activity1] score of 10 to 23", "dictionaryId": "SyntaxTemplateDictionary_2", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_348", "code": "C25370", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Exclusion Criteria", "instanceType": "Code"}, "identifier": "9", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_4", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": null, "instanceType": "EligibilityCriterion"}], "instanceType": "StudyCohort"}], "id": "StudyDesignPopulation_1", "name": "POP1", "label": "", "description": "Patients with Probable Mild to Moderate Alzheimer's Disease", "includesHealthySubjects": false, "plannedAge": {"id": "Range_3", "minValue": 50, "maxValue": 100, "unit": {"id": "Code_332", "code": "C29848", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Year", "instanceType": "Code"}, "isApproximate": false, "instanceType": "Range"}, "plannedCompletionNumber": {"id": "Range_1", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedEnrollmentNumber": {"id": "Range_2", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedSex": [{"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f614", "code": "C16576", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}, {"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f616", "code": "C20197", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}], "criteria": [{"category": {"id": "Code_345", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "1", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_1", "name": "Age Criteria", "label": "", "description": "The study age criterion", "text": "Subjects shall be between [min_age] and [max_age]", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_346", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "2", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_2", "name": "Pop Criteria", "label": "", "description": "The study population criterion", "text": "[StudyPopulation] as defined by the NINCDS and the ADRDA guidelines (Attachment LZZT.7)", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_347", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "3", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_3", "name": "Diag Criteria", "label": "", "description": "The study diagnosis criterion", "text": "[Activity1] score of 10 to 23", "dictionaryId": "SyntaxTemplateDictionary_2", "instanceType": "EligibilityCriterion"}, {"category": {"id": "Code_348", "code": "C25370", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Exclusion Criteria", "instanceType": "Code"}, "identifier": "9", "previousId": null, "nextId": null, "contextId": null, "id": "EligibilityCriterion_4", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion"}], "instanceType": "StudyDesignPopulation"}, "objectives": [{"level": {"id": "Code_335", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Primary Objective", "instanceType": "Code"}, "endpoints": [{"purpose": "", "level": {"id": "Code_334", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}, "id": "Endpoint_1", "name": "END1", "label": "", "description": "", "text": "Alzheimer's Disease Assessment Scale - Cognitive Subscale, total of 11 items [ADAS-Cog (11)] at Week 24", "dictionaryId": null, "instanceType": "Endpoint"}, {"purpose": "", "level": {"id": "Code_336", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}, "id": "Endpoint_2", "name": "END2", "label": "", "description": "", "text": "Video-referenced Clinician’s Interview-based Impression of Change (CIBIC+) at Week 24", "dictionaryId": null, "instanceType": "Endpoint"}], "id": "Objective_1", "name": "OBJ1", "label": "", "description": "Main objective", "text": "To determine if there is a statistically significant relationship (overall Type 1 erroralpha=0.05) between the change in both the ADAS-Cog (11) and CIBIC+ scores, and drug dose (0, 50 cm2 [54 mg], and 75 cm2 [81 mg]).", "dictionaryId": null, "instanceType": "Objective"}, {"level": {"id": "Code_338", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Primary Objective", "instanceType": "Code"}, "endpoints": [{"purpose": "", "level": {"id": "Code_337", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}, "id": "Endpoint_3", "name": "END3", "label": "", "description": "", "text": "Adverse events", "dictionaryId": null, "instanceType": "Endpoint"}, {"purpose": "", "level": {"id": "Code_339", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}, "id": "Endpoint_4", "name": "END4", "label": "", "description": "", "text": "Vital signs (weight, standing and supine blood pressure, heart rate)", "dictionaryId": null, "instanceType": "Endpoint"}, {"purpose": "", "level": {"id": "Code_340", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}, "id": "Endpoint_5", "name": "END5", "label": "", "description": "The change from baseline laboratory value will be  calculated as the difference between the baseline lab value and the endpoint value (i.e., the value at the specified visit) or the end of treatment observation", "text": "Laboratory evaluations (Change from Baseline)", "dictionaryId": null, "instanceType": "Endpoint"}], "id": "Objective_2", "name": "OBJ2", "label": "", "description": "Safety", "text": "To document the safety profile of the xanomeline TTS.", "dictionaryId": null, "instanceType": "Objective"}, {"level": {"id": "Code_342", "code": "C85827", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Secondary Objective", "instanceType": "Code"}, "endpoints": [{"purpose": "", "level": {"id": "Code_341", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}, "id": "Endpoint_6", "name": "END6", "label": "", "description": "", "text": "Alzheimer's Disease Assessment Scale - Cognitive Subscale, total of 11 items [ADAS-Cog (11)] at Weeks 8 and 16", "dictionaryId": null, "instanceType": "Endpoint"}, {"purpose": "", "level": {"id": "Code_343", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}, "id": "Endpoint_7", "name": "END7", "label": "", "description": "", "text": "Video-referenced Clinician’s Interview-based Impression of Change (CIBIC+) at Weeks 8 and 16", "dictionaryId": null, "instanceType": "Endpoint"}, {"purpose": "", "level": {"id": "Code_344", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}, "id": "Endpoint_8", "name": "END8", "label": "", "description": "", "text": "Mean Revised Neuropsychiatric Inventory (NPI-X) from Week 4 to Week 24", "dictionaryId": null, "instanceType": "Endpoint"}], "id": "Objective_3", "name": "OBJ3", "label": "", "description": "Behaviour", "text": "To assess the dose-dependent improvement in behavior. Improved scores on the Revised Neuropsychiatric Inventory (NPI-X) will indicate improvement in these\nareas.", "dictionaryId": null, "instanceType": "Objective"}], "scheduleTimelines": [{"id": "ScheduleTimeline_4", "name": "Main Timeline", "label": "Main Timeline", "description": "This is the main timeline for the study design.", "entryCondition": "Potential subject identified", "entryId": "ScheduledActivityInstance_9", "exits": [{"id": "ScheduleTimelineExit_4", "instanceType": "ScheduleTimelineExit"}], "mainTimeline": true, "timings": [{"id": "Timing_1", "name": "TIM1", "label": "Screening", "type": {"id": "Code_14", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Before", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 weeks", "description": "Screening timing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_9", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_15", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_2", "name": "TIM2", "label": "Pre dose", "type": {"id": "Code_16", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Before", "instanceType": "Code"}, "value": "P2D", "valueLabel": "2 days", "description": "Pre dose timing", "windowLabel": "-4..0 hours", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_10", "windowLower": "PT4H", "windowUpper": "PT0H", "relativeToFrom": {"id": "Code_17", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_3", "name": "TIM3", "label": "<PERSON><PERSON>", "type": {"id": "Code_19", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "description": "Dosing anchor", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_20", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_4", "name": "TIM4", "label": "Week 2", "type": {"id": "Code_21", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "description": "Week 2 timing", "windowLabel": "-3..3 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_12", "windowLower": "P3D", "windowUpper": "P3D", "relativeToFrom": {"id": "Code_22", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_5", "name": "TIM5", "label": "Week 4", "type": {"id": "Code_24", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P4W", "valueLabel": "4 Weeks", "description": "Week 4 timing", "windowLabel": "-3..3 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_13", "windowLower": "P3D", "windowUpper": "P3D", "relativeToFrom": {"id": "Code_25", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_6", "name": "TIM6", "label": "Week 6", "type": {"id": "Code_27", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P6W", "valueLabel": "6 Weeks", "description": "Week 6 timing", "windowLabel": "-3..3 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_14", "windowLower": "P3D", "windowUpper": "P3D", "relativeToFrom": {"id": "Code_28", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_7", "name": "TIM7", "label": "Week 8", "type": {"id": "Code_30", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P8W", "valueLabel": "8 Weeks", "description": "Week 8 timing", "windowLabel": "-3..3 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_15", "windowLower": "P3D", "windowUpper": "P3D", "relativeToFrom": {"id": "Code_31", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_9", "name": "TIM9", "label": "Week 12", "type": {"id": "Code_35", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P12W", "valueLabel": "12 Weeks", "description": "Week 12 timing", "windowLabel": "-4..4 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_17", "windowLower": "P4D", "windowUpper": "P4D", "relativeToFrom": {"id": "Code_36", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_11", "name": "TIM11", "label": "Week 16", "type": {"id": "Code_40", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P16W", "valueLabel": "16 Weeks", "description": "Week 16 timing", "windowLabel": "-4..4 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_19", "windowLower": "P4D", "windowUpper": "P4D", "relativeToFrom": {"id": "Code_41", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_13", "name": "TIM13", "label": "Week 20", "type": {"id": "Code_45", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P20W", "valueLabel": "20 Weeks", "description": "Week 20 timing", "windowLabel": "-4..4 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_21", "windowLower": "P4D", "windowUpper": "P4D", "relativeToFrom": {"id": "Code_46", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_15", "name": "TIM15", "label": "Week 24", "type": {"id": "Code_50", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P24W", "valueLabel": "24 Weeks", "description": "Week 24 timing", "windowLabel": "-4..4 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_23", "windowLower": "P4D", "windowUpper": "P4D", "relativeToFrom": {"id": "Code_51", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_16", "name": "TIM16", "label": "Week 26", "type": {"id": "Code_53", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P26W", "valueLabel": "26 Weeks", "description": "Week 26 timing", "windowLabel": "-3..3 days", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_24", "windowLower": "P3D", "windowUpper": "P3D", "relativeToFrom": {"id": "Code_54", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_8", "name": "TIM8", "label": "Week 8 Home", "type": {"id": "Code_33", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "description": "Week 8 at home timing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_15", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_16", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_34", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_10", "name": "TIM10", "label": "Week 12 Home", "type": {"id": "Code_38", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "description": "Week 12 at home timing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_17", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_18", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_39", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_12", "name": "TIM12", "label": "Week 16 Home", "type": {"id": "Code_43", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "description": "Week 16 at home timing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_19", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_20", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_44", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_14", "name": "TIM14", "label": "Week 20 Home", "type": {"id": "Code_48", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "description": "Week 20 at home timing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_21", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_22", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_49", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}], "instances": [{"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_1", "activityIds": ["Activity_1", "Activity_2", "Activity_3", "Activity_4", "Activity_5", "Activity_6", "Activity_7", "Activity_8", "Activity_9", "Activity_10", "Activity_13", "Activity_16", "Activity_17", "Activity_18", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_24", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "id": "ScheduledActivityInstance_9", "name": "SCREEN1", "label": "Screen One", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_10", "epochId": "StudyEpoch_1"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_2", "activityIds": ["Activity_13", "Activity_14"], "id": "ScheduledActivityInstance_10", "name": "SCREEN2", "label": "Screen Two", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_11", "epochId": "StudyEpoch_1"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_3", "activityIds": ["Activity_12", "Activity_13", "Activity_15", "Activity_19", "Activity_23", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "id": "ScheduledActivityInstance_11", "name": "DOSE", "label": "<PERSON><PERSON>", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_12", "epochId": "StudyEpoch_2"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_4", "activityIds": ["Activity_11", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30"], "id": "ScheduledActivityInstance_12", "name": "WK2", "label": "Week 2", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_13", "epochId": "StudyEpoch_2"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_5", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "id": "ScheduledActivityInstance_13", "name": "WK4", "label": "Week 4", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_14", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_6", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "id": "ScheduledActivityInstance_14", "name": "WK6", "label": "Week 6", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_15", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_7", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "id": "ScheduledActivityInstance_15", "name": "WK8", "label": "Week 8", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_16", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_7", "activityIds": ["Activity_30"], "id": "ScheduledActivityInstance_16", "name": "WK8N", "label": "Week NPI", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_17", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_8", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30"], "id": "ScheduledActivityInstance_17", "name": "WK12", "label": "Week 12", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_18", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_8", "activityIds": ["Activity_30"], "id": "ScheduledActivityInstance_18", "name": "WK12N", "label": "Week 12 NPI", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_19", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_9", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "id": "ScheduledActivityInstance_19", "name": "WK16", "label": "Week 16", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_20", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_9", "activityIds": ["Activity_30"], "id": "ScheduledActivityInstance_20", "name": "WK16N", "label": "Week 16 NPI", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_21", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_10", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "id": "ScheduledActivityInstance_21", "name": "WK20", "label": "Week 20", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_22", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_10", "activityIds": ["Activity_30"], "id": "ScheduledActivityInstance_22", "name": "WK20N", "label": "Week 20 NPI", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_23", "epochId": "StudyEpoch_3"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_11", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "id": "ScheduledActivityInstance_23", "name": "WK24", "label": "Week 24", "description": "-", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_24", "epochId": "StudyEpoch_4"}, {"instanceType": "ScheduledActivityInstance", "encounterId": "Encounter_12", "activityIds": ["Activity_7", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_26", "Activity_30"], "id": "ScheduledActivityInstance_24", "name": "WK26", "label": "Week 26", "description": "-", "timelineExitId": "ScheduleTimelineExit_4", "timelineId": null, "defaultConditionId": null, "epochId": "StudyEpoch_5"}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_1", "name": "Adverse Event Timeline", "label": "Adverse Event Timeline", "description": "This is the adverse event timeline", "entryCondition": "Subject suffers an adverse event", "entryId": "ScheduledActivityInstance_1", "exits": [{"id": "ScheduleTimelineExit_1", "instanceType": "ScheduleTimelineExit"}], "mainTimeline": false, "timings": [{"id": "Timing_17", "name": "TIM17", "label": "Adverse Event", "type": {"id": "Code_56", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "description": "Adverse Event", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_1", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_1", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_57", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}], "instances": [{"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_31"], "id": "ScheduledActivityInstance_1", "name": "AE", "label": "Adevers Event", "description": "-", "timelineExitId": "ScheduleTimelineExit_1", "timelineId": null, "defaultConditionId": null, "epochId": null}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_2", "name": "Early Termination Timeline", "label": "Early Termination Timeline", "description": "This is the early termination processing", "entryCondition": "Subject terminates the study early", "entryId": "ScheduledActivityInstance_2", "exits": [{"id": "ScheduleTimelineExit_2", "instanceType": "ScheduleTimelineExit"}], "mainTimeline": false, "timings": [{"id": "Timing_18", "name": "TIM18", "label": "Early Termination", "type": {"id": "Code_58", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "description": "Early Termination", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_2", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_2", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_59", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}], "instances": [{"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_7", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_26", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_32"], "id": "ScheduledActivityInstance_2", "name": "ET", "label": "Early Termination", "description": "-", "timelineExitId": "ScheduleTimelineExit_2", "timelineId": null, "defaultConditionId": null, "epochId": null}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_3", "name": "Vital Sign Blood Pressure Timeline", "label": "Vital Sign Blood Pressure Timeline", "description": "BP Profile", "entryCondition": "Automatic execution", "entryId": "ScheduledActivityInstance_3", "exits": [{"id": "ScheduleTimelineExit_3", "instanceType": "ScheduleTimelineExit"}], "mainTimeline": false, "timings": [{"id": "Timing_19", "name": "TIM19", "label": "<PERSON><PERSON>", "type": {"id": "Code_60", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 mins", "description": "<PERSON><PERSON>", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_3", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_61", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_20", "name": "TIM20", "label": "VS while supine", "type": {"id": "Code_62", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT5M", "valueLabel": "5 mins", "description": "VS while supine", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_4", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_63", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_21", "name": "TIM21", "label": "Standing", "type": {"id": "Code_64", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 min", "description": "Standing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_4", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_5", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_65", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "End to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_22", "name": "TIM22", "label": "VS while standing", "type": {"id": "Code_66", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT1M", "valueLabel": "1 min", "description": "VS while standing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_5", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_6", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_67", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_23", "name": "TIM23", "label": "Standing", "type": {"id": "Code_68", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 min", "description": "Standing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_6", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_7", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_69", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "End to Start", "instanceType": "Code"}, "instanceType": "Timing"}, {"id": "Timing_24", "name": "TIM24", "label": "VS while standing", "type": {"id": "Code_70", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT2M", "valueLabel": "2 min", "description": "VS while standing", "windowLabel": "", "relativeToScheduledInstanceId": "ScheduledActivityInstance_7", "relativeFromScheduledInstanceId": "ScheduledActivityInstance_8", "windowLower": null, "windowUpper": null, "relativeToFrom": {"id": "Code_71", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "instanceType": "Timing"}], "instances": [{"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_33"], "id": "ScheduledActivityInstance_3", "name": "VS_5MIN", "label": "5 minute supine", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_4", "epochId": null}, {"instanceType": "ScheduledDecisionInstance", "conditionAssignments": [{"id": "ConditionAssignment_1", "condition": "Dummy-ConditionAssignment", "conditionTargetId": "ScheduledActivityInstance_5", "instanceType": "ConditionAssignment"}], "id": "ScheduledDecisionInstance_1", "name": "NotAvailable", "label": "Dummy-Data", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "", "epochId": null}, {"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_34"], "id": "ScheduledActivityInstance_4", "name": "VS_SUPINE", "label": "Vital signs supine", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_5", "epochId": null}, {"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_35"], "id": "ScheduledActivityInstance_5", "name": "VS_1MIN", "label": "1 minute standing", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_6", "epochId": null}, {"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_36"], "id": "ScheduledActivityInstance_6", "name": "VS_STAND1", "label": "Vital signs after 1 min standing", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_7", "epochId": null}, {"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_35"], "id": "ScheduledActivityInstance_7", "name": "VS_2MIN", "label": "2 minute standing", "description": "", "timelineExitId": null, "timelineId": null, "defaultConditionId": "ScheduledActivityInstance_8", "epochId": null}, {"instanceType": "ScheduledActivityInstance", "encounterId": null, "activityIds": ["Activity_36"], "id": "ScheduledActivityInstance_8", "name": "VS_STAND3", "label": "<PERSON>l signs after 3 min standing", "description": "", "timelineExitId": "ScheduleTimelineExit_3", "timelineId": null, "defaultConditionId": null, "epochId": null}], "instanceType": "ScheduleTimeline"}], "therapeuticAreas": [{"id": "Code_133", "code": "MILD_MOD_ALZ", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Mild to Moderate Alzheimer's Disease", "instanceType": "Code"}, {"id": "Code_134", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer's disease", "instanceType": "Code"}], "estimands": [{"id": "0bc02031-b058-43f8-9888-0f66f59b825f", "interventionId": "interventionId", "summaryMeasure": "TEST", "analysisPopulation": {"id": "77af2751-1707-4edc-a5db-350ebe7233d8", "name": "analysisName", "label": "label", "description": "Population 1", "text": "text", "instanceType": "AnalysisPopulation"}, "variableOfInterestId": "Endpoint_1", "intercurrentEvents": [{"id": "1dc5c1e9-7b21-4455-9f37-ff2fadb8ac65", "name": "Event Name", "label": "name", "description": "Event Description", "strategy": "Strategies", "instanceType": "IntercurrentEvent"}], "instanceType": "<PERSON><PERSON><PERSON><PERSON>"}], "encounters": [{"id": "Encounter_1", "name": "E1", "label": "Screening 1", "description": "Screening encounter", "contactModes": [{"id": "Code_84", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_83", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_82", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_2", "previousId": null, "scheduledAtId": null, "transitionStartRule": {"id": "TransitionRule_1", "name": "ENCOUNTER_START_RULE_1", "label": null, "description": null, "text": "Subject identifier", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_2", "name": "ENCOUNTER_START_RULE_1", "label": null, "description": null, "text": "completion of screening activities", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_2", "name": "E2", "label": "Screening 2", "description": "Screening encounter - Ambulatory ECG Placement", "contactModes": [{"id": "Code_87", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_86", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_85", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_3", "previousId": "Encounter_1", "scheduledAtId": "Timing_2", "transitionStartRule": null, "transitionEndRule": {"id": "TransitionRule_3", "name": "ENCOUNTER_START_RULE_2", "label": null, "description": null, "text": "subject leaves clinic after connection of ambulatory ECG machine", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_3", "name": "E3", "label": "Baseline", "description": "Baseline encounter - Ambulatory ECG Removal", "contactModes": [{"id": "Code_90", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_89", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_88", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_4", "previousId": "Encounter_2", "scheduledAtId": null, "transitionStartRule": {"id": "TransitionRule_4", "name": "ENCOUNTER_START_RULE_3", "label": null, "description": null, "text": "subject has connection of ambulatory ECG machine removed", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_5", "name": "ENCOUNTER_START_RULE_3", "label": null, "description": null, "text": "Radomized", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_4", "name": "E4", "label": "Week 2", "description": "Day 14", "contactModes": [{"id": "Code_93", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_92", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_91", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_5", "previousId": "Encounter_3", "scheduledAtId": "Timing_4", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_5", "name": "E5", "label": "Week 4", "description": "Day 28", "contactModes": [{"id": "Code_96", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_95", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_94", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_6", "previousId": "Encounter_4", "scheduledAtId": "Timing_5", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_6", "name": "E7", "label": "Week 6", "description": "Day 42", "contactModes": [{"id": "Code_99", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_98", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_97", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_7", "previousId": "Encounter_5", "scheduledAtId": "Timing_6", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_7", "name": "E8", "label": "Week 8", "description": "Day 56", "contactModes": [{"id": "Code_102", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_103", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_101", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_100", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_8", "previousId": "Encounter_6", "scheduledAtId": "Timing_7", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_8", "name": "E9", "label": "Week 12", "description": "Day 84", "contactModes": [{"id": "Code_106", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_107", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_105", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_104", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_9", "previousId": "Encounter_7", "scheduledAtId": "Timing_9", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_9", "name": "E10", "label": "Week 16", "description": "Day 112", "contactModes": [{"id": "Code_110", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_111", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_109", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_108", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_10", "previousId": "Encounter_8", "scheduledAtId": "Timing_11", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_10", "name": "E11", "label": "Week 20", "description": "Day 140", "contactModes": [{"id": "Code_114", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_115", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_113", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_112", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_11", "previousId": "Encounter_9", "scheduledAtId": "Timing_13", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_11", "name": "E12", "label": "Week 24", "description": "Day 168", "contactModes": [{"id": "Code_118", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_117", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_116", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": "Encounter_12", "previousId": "Encounter_10", "scheduledAtId": "Timing_15", "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_12", "name": "E13", "label": "Week 26", "description": "Day 182", "contactModes": [{"id": "Code_121", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "environmentalSetting": [{"id": "Code_120", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "type": {"id": "Code_119", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "nextId": null, "previousId": "Encounter_11", "scheduledAtId": "Timing_16", "transitionStartRule": null, "transitionEndRule": {"id": "TransitionRule_6", "name": "ENCOUNTER_START_RULE_12", "label": null, "description": null, "text": "End of treatment", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}], "activities": [{"id": "Activity_1", "name": "Informed consent", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_2", "previousId": null, "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_2", "name": "Inclusion/exclusion criteria", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_3", "previousId": "Activity_1", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_3", "name": "Patient number assigned", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_4", "previousId": "Activity_2", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_4", "name": "Demographics", "label": "Demographics", "description": "", "definedProcedures": [], "nextId": "Activity_5", "previousId": "Activity_3", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_5", "name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "description": "", "definedProcedures": [], "nextId": "Activity_6", "previousId": "Activity_4", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_6", "name": "MMSE", "label": "MMSE", "description": "", "definedProcedures": [], "nextId": "Activity_7", "previousId": "Activity_5", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_7", "name": "Physical examination", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_8", "previousId": "Activity_6", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_8", "name": "Medical history", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_9", "previousId": "Activity_7", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_9", "name": "Habits", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_10", "previousId": "Activity_8", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_10", "name": "Chest X-ray", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_11", "previousId": "Activity_9", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_11", "name": "Apo E genotyping", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_12", "previousId": "Activity_10", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_12", "name": "Patient randomised", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_13", "previousId": "Activity_11", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_13", "name": "Vital signs / Temperature", "label": "Vital Signs and Temperature", "description": "", "definedProcedures": [], "nextId": "Activity_14", "previousId": "Activity_12", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_20", "BiomedicalConcept_21", "BiomedicalConcept_22"], "timelineId": "ScheduleTimeline_3", "instanceType": "Activity"}, {"id": "Activity_14", "name": "Ambulatory ECG placed", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_15", "previousId": "Activity_13", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_15", "name": "Ambulatory ECG removed", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_16", "previousId": "Activity_14", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_16", "name": "ECG", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_17", "previousId": "Activity_15", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_17", "name": "Placebo TTS test", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_18", "previousId": "Activity_16", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_18", "name": "CT scan", "label": "", "description": "", "definedProcedures": [{"id": "Procedure_1", "name": "PR1", "label": "CT Scan", "description": "CT Scan", "procedureType": "CT Scan", "code": {"id": "Code_79", "code": "383371000119108", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "CT of head without contrast", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "nextId": "Activity_19", "previousId": "Activity_17", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_19", "name": "Concomitant medications", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_20", "previousId": "Activity_18", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_20", "name": "Hematology", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_21", "previousId": "Activity_19", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_21", "name": "Chemistry", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_22", "previousId": "Activity_20", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_23", "BiomedicalConcept_24", "BiomedicalConcept_25", "BiomedicalConcept_26", "BiomedicalConcept_27", "BiomedicalConcept_28", "BiomedicalConcept_29"], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_22", "name": "Uninalysis", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_23", "previousId": "Activity_21", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_23", "name": "Plasma Specimen (Xanomeline)", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_24", "previousId": "Activity_22", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_24", "name": "Hemoglobin A1C", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_25", "previousId": "Activity_23", "bcCategoryIds": [], "bcSurrogateIds": ["BiomedicalConceptSurrogate_1"], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_25", "name": "Study drug", "label": "Study drug record , Medications dispensed, Medications returned", "description": "", "definedProcedures": [], "nextId": "Activity_26", "previousId": "Activity_24", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_26", "name": "TTS Acceptability Survey", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_27", "previousId": "Activity_25", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_27", "name": "ADAS-Cog", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_28", "previousId": "Activity_26", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_28", "name": "CIBIC+", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_29", "previousId": "Activity_27", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_29", "name": "DAD", "label": "", "description": "", "definedProcedures": [], "nextId": "Activity_30", "previousId": "Activity_28", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_30", "name": "NPI-X", "label": "", "description": "", "definedProcedures": [], "nextId": null, "previousId": "Activity_29", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_31", "name": "Adverse events", "label": "", "description": "", "definedProcedures": [], "nextId": null, "previousId": null, "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_1"], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_32", "name": "Check adverse events", "label": "", "description": "", "definedProcedures": [], "nextId": null, "previousId": "Activity_30", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "ScheduleTimeline_1", "instanceType": "Activity"}, {"id": "Activity_33", "name": "<PERSON><PERSON>", "label": "Subject supine", "description": "", "definedProcedures": [{"id": "Procedure_2", "name": "PR_SUPINE", "label": "", "description": "Subject Supine", "procedureType": "", "code": {"id": "Code_80", "code": "123", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Subject Supine", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "nextId": "Activity_34", "previousId": null, "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_34", "name": "Vital Signs Supine", "label": "Vital signs while supine", "description": "", "definedProcedures": [], "nextId": "Activity_35", "previousId": "Activity_33", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_14", "BiomedicalConcept_15", "BiomedicalConcept_16"], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_35", "name": "Stand", "label": "Subject Standing", "description": "", "definedProcedures": [{"id": "Procedure_3", "name": "PR_STAND", "label": "", "description": "Subject Standing", "procedureType": "", "code": {"id": "Code_81", "code": "124", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Subject Standing", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "nextId": "Activity_36", "previousId": "Activity_34", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_36", "name": "Vital Signs Standing", "label": "Vital signs while standing", "description": "", "definedProcedures": [], "nextId": null, "previousId": "Activity_35", "bcCategoryIds": [], "bcSurrogateIds": [], "biomedicalConceptIds": ["BiomedicalConcept_17", "BiomedicalConcept_18", "BiomedicalConcept_19"], "timelineId": "", "instanceType": "Activity"}], "rationale": "The discontinuation rate associated with this oral dosing regimen was 58.6% in previous studies, and alternative clinical strategies have been sought to improve tolerance for the compound. To that end, development of a Transdermal Therapeutic System (TTS) has been initiated.", "blindingSchema": {"id": "AliasCode_4", "standardCode": {"id": "Code_135", "code": "C15228", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Double Blind Study", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "biomedicalConcepts": [{"id": "BiomedicalConcept_20", "name": "Body Temperature", "label": "Body Temperature", "synonyms": ["Temperature"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C174446", "properties": [{"id": "BiomedicalConceptProperty_98", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_122", "standardCode": {"id": "Code_278", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_99", "name": "Unit of Temperature", "label": "Unit of Temperature", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_18", "isEnabled": true, "code": {"id": "Code_279", "code": "C42537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON><PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_123", "standardCode": {"id": "Code_280", "code": "C44276", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_100", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_124", "standardCode": {"id": "Code_281", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_121", "standardCode": {"id": "Code_277", "code": "C174446", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_21", "name": "Body Weight", "label": "Body Weight", "synonyms": ["Weight"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C81328", "properties": [{"id": "BiomedicalConceptProperty_101", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_126", "standardCode": {"id": "Code_283", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_102", "name": "Unit of Weight", "label": "Unit of Weight", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_19", "isEnabled": true, "code": {"id": "Code_284", "code": "C28252", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Kilogram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_20", "isEnabled": true, "code": {"id": "Code_285", "code": "C48155", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Gram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_21", "isEnabled": true, "code": {"id": "Code_286", "code": "C48531", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pound", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_127", "standardCode": {"id": "Code_287", "code": "C48208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_125", "standardCode": {"id": "Code_282", "code": "C81328", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_22", "name": "Body Height", "label": "Body Height", "synonyms": ["Height"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C164634", "properties": [{"id": "BiomedicalConceptProperty_103", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_129", "standardCode": {"id": "Code_289", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_104", "name": "Unit of Height", "label": "Unit of Height", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_22", "isEnabled": true, "code": {"id": "Code_290", "code": "C49668", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Centimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_23", "isEnabled": true, "code": {"id": "Code_291", "code": "C48500", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inch", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_24", "isEnabled": true, "code": {"id": "Code_292", "code": "C28251", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Millimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_25", "isEnabled": true, "code": {"id": "Code_293", "code": "C41139", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>er", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_130", "standardCode": {"id": "Code_294", "code": "C168688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_128", "standardCode": {"id": "Code_288", "code": "C164634", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_23", "name": "Alanine Aminotransferase Measurement", "label": "Alanine Aminotransferase Measurement", "synonyms": ["ALT", "SGPT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64433", "properties": [{"id": "BiomedicalConceptProperty_105", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_132", "standardCode": {"id": "Code_296", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_106", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_133", "standardCode": {"id": "Code_297", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_107", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_134", "standardCode": {"id": "Code_298", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_108", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_135", "standardCode": {"id": "Code_299", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_131", "standardCode": {"id": "Code_295", "code": "C64433", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alanine Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_24", "name": "Albumin Measurement", "label": "Albumin Measurement", "synonyms": ["Albumin"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64431", "properties": [{"id": "BiomedicalConceptProperty_109", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_137", "standardCode": {"id": "Code_301", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_110", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_138", "standardCode": {"id": "Code_302", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_111", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_139", "standardCode": {"id": "Code_303", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_112", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_140", "standardCode": {"id": "Code_304", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_136", "standardCode": {"id": "Code_300", "code": "C64431", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Albumin Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_25", "name": "Alkaline Phosphatase Measurement", "label": "Alkaline Phosphatase Measurement", "synonyms": ["Alkaline Phosphatase"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64432", "properties": [{"id": "BiomedicalConceptProperty_113", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_142", "standardCode": {"id": "Code_306", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_114", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_143", "standardCode": {"id": "Code_307", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_115", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_144", "standardCode": {"id": "Code_308", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_116", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_145", "standardCode": {"id": "Code_309", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_141", "standardCode": {"id": "Code_305", "code": "C64432", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alkaline Phosphatase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_26", "name": "Aspartate Aminotransferase Measurement", "label": "Aspartate Aminotransferase Measurement", "synonyms": ["AST", "SGOT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64467", "properties": [{"id": "BiomedicalConceptProperty_117", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_147", "standardCode": {"id": "Code_311", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_118", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_148", "standardCode": {"id": "Code_312", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_119", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_149", "standardCode": {"id": "Code_313", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_120", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_150", "standardCode": {"id": "Code_314", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_146", "standardCode": {"id": "Code_310", "code": "C64467", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Aspartate Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_27", "name": "Creatinine Measurement", "label": "Creatinine Measurement", "synonyms": ["Creatinine"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64547", "properties": [{"id": "BiomedicalConceptProperty_121", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_152", "standardCode": {"id": "Code_316", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_122", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_153", "standardCode": {"id": "Code_317", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_123", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_154", "standardCode": {"id": "Code_318", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_124", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_155", "standardCode": {"id": "Code_319", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_151", "standardCode": {"id": "Code_315", "code": "C64547", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Creatinine Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_28", "name": "Potassium Measurement", "label": "Potassium Measurement", "synonyms": ["Potassium", "K"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64853", "properties": [{"id": "BiomedicalConceptProperty_125", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_157", "standardCode": {"id": "Code_321", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_126", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_158", "standardCode": {"id": "Code_322", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_127", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_159", "standardCode": {"id": "Code_323", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_128", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_160", "standardCode": {"id": "Code_324", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_156", "standardCode": {"id": "Code_320", "code": "C64853", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Potassium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_29", "name": "Sodium Measurement", "label": "Sodium Measurement", "synonyms": ["Sodium", "NA"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64809", "properties": [{"id": "BiomedicalConceptProperty_129", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_162", "standardCode": {"id": "Code_326", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_130", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_163", "standardCode": {"id": "Code_327", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_131", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_164", "standardCode": {"id": "Code_328", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_132", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_165", "standardCode": {"id": "Code_329", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_161", "standardCode": {"id": "Code_325", "code": "C64809", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Sodium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_1", "name": "Adverse Event", "label": "Adverse Event", "synonyms": ["Adverse Event Reported Term", "AETERM"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C41331", "properties": [{"id": "BiomedicalConceptProperty_1", "name": "Adverse Event Verbatim Description", "label": "Adverse Event Verbatim Description", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_6", "standardCode": {"id": "Code_145", "code": "C78541", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Verbatim Description", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_2", "name": "Adverse Event Dictionary Derived Term", "label": "Adverse Event Dictionary Derived Term", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_7", "standardCode": {"id": "Code_146", "code": "C83344", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Dictionary Derived Term", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_3", "name": "Adverse Event Category", "label": "Adverse Event Category", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_8", "standardCode": {"id": "Code_147", "code": "C83198", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Category", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_4", "name": "Adverse Event Subcategory", "label": "Adverse Event Subcategory", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_9", "standardCode": {"id": "Code_148", "code": "C83212", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Subcategory", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_5", "name": "Adverse Event Pre-specified", "label": "Adverse Event Pre-specified", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_10", "standardCode": {"id": "Code_149", "code": "C87840", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Pre-specified", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_6", "name": "Severity of Adverse Event", "label": "Severity of Adverse Event", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_11", "standardCode": {"id": "Code_150", "code": "C53253", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Severity of Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_7", "name": "Adverse Event Toxicity Grade", "label": "Adverse Event Toxicity Grade", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_12", "standardCode": {"id": "Code_151", "code": "C78605", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Toxicity Grade", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_8", "name": "Seriousness of Adverse Event", "label": "Seriousness of Adverse Event", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_13", "standardCode": {"id": "Code_152", "code": "C53252", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Seriousness of Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_9", "name": "Adverse Event Action Taken with Study Treatment", "label": "Adverse Event Action Taken with Study Treatment", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_14", "standardCode": {"id": "Code_153", "code": "C83013", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Action Taken with Study Treatment", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_10", "name": "Adverse Event Attribution to Product or Procedure", "label": "Adverse Event Attribution to Product or Procedure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_15", "standardCode": {"id": "Code_154", "code": "C41358", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Attribution to Product or Procedure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_11", "name": "Adverse Event Pattern", "label": "Adverse Event Pattern", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_1", "isEnabled": true, "code": {"id": "Code_155", "code": "C71325", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Intermittent", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_16", "standardCode": {"id": "Code_156", "code": "C83208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Pattern", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_12", "name": "Adverse Event Outcome", "label": "Adverse Event Outcome", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_17", "standardCode": {"id": "Code_157", "code": "C49489", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Outcome", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_13", "name": "Adverse Event Start Date Time", "label": "Adverse Event Start Date Time", "isRequired": true, "isEnabled": true, "datatype": "datetime", "responseCodes": [], "code": {"id": "AliasCode_18", "standardCode": {"id": "Code_158", "code": "C83215", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Start Date Time", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_14", "name": "Adverse Event End Date Time", "label": "Adverse Event End Date Time", "isRequired": true, "isEnabled": true, "datatype": "datetime", "responseCodes": [], "code": {"id": "AliasCode_19", "standardCode": {"id": "Code_159", "code": "C83201", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event End Date Time", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_5", "standardCode": {"id": "Code_144", "code": "C41331", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_2", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_15", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_21", "standardCode": {"id": "Code_161", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_16", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_2", "isEnabled": true, "code": {"id": "Code_162", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_22", "standardCode": {"id": "Code_163", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_17", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_23", "standardCode": {"id": "Code_164", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_18", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_24", "standardCode": {"id": "Code_165", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_19", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_25", "standardCode": {"id": "Code_166", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_20", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_26", "standardCode": {"id": "Code_167", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_20", "standardCode": {"id": "Code_160", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_3", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_21", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_28", "standardCode": {"id": "Code_169", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_22", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_3", "isEnabled": true, "code": {"id": "Code_170", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_29", "standardCode": {"id": "Code_171", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_23", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_30", "standardCode": {"id": "Code_172", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_24", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_31", "standardCode": {"id": "Code_173", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_25", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_32", "standardCode": {"id": "Code_174", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_26", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_33", "standardCode": {"id": "Code_175", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_27", "standardCode": {"id": "Code_168", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_4", "name": "Body Temperature", "label": "Body Temperature", "synonyms": ["Temperature"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C174446", "properties": [{"id": "BiomedicalConceptProperty_27", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_35", "standardCode": {"id": "Code_177", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_28", "name": "Unit of Temperature", "label": "Unit of Temperature", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_4", "isEnabled": true, "code": {"id": "Code_178", "code": "C42537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON><PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_36", "standardCode": {"id": "Code_179", "code": "C44276", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_29", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_37", "standardCode": {"id": "Code_180", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_34", "standardCode": {"id": "Code_176", "code": "C174446", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_5", "name": "Body Weight", "label": "Body Weight", "synonyms": ["Weight"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C81328", "properties": [{"id": "BiomedicalConceptProperty_30", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_39", "standardCode": {"id": "Code_182", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_31", "name": "Unit of Weight", "label": "Unit of Weight", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_5", "isEnabled": true, "code": {"id": "Code_183", "code": "C28252", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Kilogram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_6", "isEnabled": true, "code": {"id": "Code_184", "code": "C48155", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Gram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_7", "isEnabled": true, "code": {"id": "Code_185", "code": "C48531", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pound", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_40", "standardCode": {"id": "Code_186", "code": "C48208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_38", "standardCode": {"id": "Code_181", "code": "C81328", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_6", "name": "Body Height", "label": "Body Height", "synonyms": ["Height"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C164634", "properties": [{"id": "BiomedicalConceptProperty_32", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_42", "standardCode": {"id": "Code_188", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_33", "name": "Unit of Height", "label": "Unit of Height", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_8", "isEnabled": true, "code": {"id": "Code_189", "code": "C49668", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Centimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_9", "isEnabled": true, "code": {"id": "Code_190", "code": "C48500", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inch", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_10", "isEnabled": true, "code": {"id": "Code_191", "code": "C28251", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Millimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_11", "isEnabled": true, "code": {"id": "Code_192", "code": "C41139", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>er", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_43", "standardCode": {"id": "Code_193", "code": "C168688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_41", "standardCode": {"id": "Code_187", "code": "C164634", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_7", "name": "Alanine Aminotransferase Measurement", "label": "Alanine Aminotransferase Measurement", "synonyms": ["ALT", "SGPT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64433", "properties": [{"id": "BiomedicalConceptProperty_34", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_45", "standardCode": {"id": "Code_195", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_35", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_46", "standardCode": {"id": "Code_196", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_36", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_47", "standardCode": {"id": "Code_197", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_37", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_48", "standardCode": {"id": "Code_198", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_44", "standardCode": {"id": "Code_194", "code": "C64433", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alanine Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_8", "name": "Albumin Measurement", "label": "Albumin Measurement", "synonyms": ["Albumin"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64431", "properties": [{"id": "BiomedicalConceptProperty_38", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_50", "standardCode": {"id": "Code_200", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_39", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_51", "standardCode": {"id": "Code_201", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_40", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_52", "standardCode": {"id": "Code_202", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_41", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_53", "standardCode": {"id": "Code_203", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_49", "standardCode": {"id": "Code_199", "code": "C64431", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Albumin Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_9", "name": "Alkaline Phosphatase Measurement", "label": "Alkaline Phosphatase Measurement", "synonyms": ["Alkaline Phosphatase"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64432", "properties": [{"id": "BiomedicalConceptProperty_42", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_55", "standardCode": {"id": "Code_205", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_43", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_56", "standardCode": {"id": "Code_206", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_44", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_57", "standardCode": {"id": "Code_207", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_45", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_58", "standardCode": {"id": "Code_208", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_54", "standardCode": {"id": "Code_204", "code": "C64432", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alkaline Phosphatase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_10", "name": "Aspartate Aminotransferase Measurement", "label": "Aspartate Aminotransferase Measurement", "synonyms": ["AST", "SGOT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64467", "properties": [{"id": "BiomedicalConceptProperty_46", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_60", "standardCode": {"id": "Code_210", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_47", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_61", "standardCode": {"id": "Code_211", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_48", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_62", "standardCode": {"id": "Code_212", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_49", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_63", "standardCode": {"id": "Code_213", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_59", "standardCode": {"id": "Code_209", "code": "C64467", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Aspartate Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_11", "name": "Creatinine Measurement", "label": "Creatinine Measurement", "synonyms": ["Creatinine"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64547", "properties": [{"id": "BiomedicalConceptProperty_50", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_65", "standardCode": {"id": "Code_215", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_51", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_66", "standardCode": {"id": "Code_216", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_52", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_67", "standardCode": {"id": "Code_217", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_53", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_68", "standardCode": {"id": "Code_218", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_64", "standardCode": {"id": "Code_214", "code": "C64547", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Creatinine Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_12", "name": "Potassium Measurement", "label": "Potassium Measurement", "synonyms": ["Potassium", "K"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64853", "properties": [{"id": "BiomedicalConceptProperty_54", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_70", "standardCode": {"id": "Code_220", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_55", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_71", "standardCode": {"id": "Code_221", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_56", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_72", "standardCode": {"id": "Code_222", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_57", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_73", "standardCode": {"id": "Code_223", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_69", "standardCode": {"id": "Code_219", "code": "C64853", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Potassium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_13", "name": "Sodium Measurement", "label": "Sodium Measurement", "synonyms": ["Sodium", "NA"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64809", "properties": [{"id": "BiomedicalConceptProperty_58", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_75", "standardCode": {"id": "Code_225", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_59", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_76", "standardCode": {"id": "Code_226", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_60", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_77", "standardCode": {"id": "Code_227", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_61", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_78", "standardCode": {"id": "Code_228", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_74", "standardCode": {"id": "Code_224", "code": "C64809", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Sodium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_14", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_62", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_80", "standardCode": {"id": "Code_230", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_63", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_12", "isEnabled": true, "code": {"id": "Code_231", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_81", "standardCode": {"id": "Code_232", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_64", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_82", "standardCode": {"id": "Code_233", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_65", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_83", "standardCode": {"id": "Code_234", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_66", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_84", "standardCode": {"id": "Code_235", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_67", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_85", "standardCode": {"id": "Code_236", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_79", "standardCode": {"id": "Code_229", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_15", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_68", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_87", "standardCode": {"id": "Code_238", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_69", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_13", "isEnabled": true, "code": {"id": "Code_239", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_88", "standardCode": {"id": "Code_240", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_70", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_89", "standardCode": {"id": "Code_241", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_71", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_90", "standardCode": {"id": "Code_242", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_72", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_91", "standardCode": {"id": "Code_243", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_73", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_92", "standardCode": {"id": "Code_244", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_86", "standardCode": {"id": "Code_237", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_16", "name": "Heart Rate", "label": "Heart Rate", "synonyms": [], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C49677", "properties": [{"id": "BiomedicalConceptProperty_74", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "integer", "responseCodes": [], "code": {"id": "AliasCode_94", "standardCode": {"id": "Code_246", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_75", "name": "Count per Minute", "label": "Count per Minute", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_14", "isEnabled": true, "code": {"id": "Code_247", "code": "C49673", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Beats per Minute", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_95", "standardCode": {"id": "Code_248", "code": "C73688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Count per Minute", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_76", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_96", "standardCode": {"id": "Code_249", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_77", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_97", "standardCode": {"id": "Code_250", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_78", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_98", "standardCode": {"id": "Code_251", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_79", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_99", "standardCode": {"id": "Code_252", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_93", "standardCode": {"id": "Code_245", "code": "C49677", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Heart Rate", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_17", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_80", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_101", "standardCode": {"id": "Code_254", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_81", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_15", "isEnabled": true, "code": {"id": "Code_255", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_102", "standardCode": {"id": "Code_256", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_82", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_103", "standardCode": {"id": "Code_257", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_83", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_104", "standardCode": {"id": "Code_258", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_84", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_105", "standardCode": {"id": "Code_259", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_85", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_106", "standardCode": {"id": "Code_260", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_100", "standardCode": {"id": "Code_253", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_18", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_86", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_108", "standardCode": {"id": "Code_262", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_87", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_16", "isEnabled": true, "code": {"id": "Code_263", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_109", "standardCode": {"id": "Code_264", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_88", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_110", "standardCode": {"id": "Code_265", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_89", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_111", "standardCode": {"id": "Code_266", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_90", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_112", "standardCode": {"id": "Code_267", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_91", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_113", "standardCode": {"id": "Code_268", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_107", "standardCode": {"id": "Code_261", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_19", "name": "Heart Rate", "label": "Heart Rate", "synonyms": [], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C49677", "properties": [{"id": "BiomedicalConceptProperty_92", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "integer", "responseCodes": [], "code": {"id": "AliasCode_115", "standardCode": {"id": "Code_270", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_93", "name": "Count per Minute", "label": "Count per Minute", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_17", "isEnabled": true, "code": {"id": "Code_271", "code": "C49673", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Beats per Minute", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_116", "standardCode": {"id": "Code_272", "code": "C73688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Count per Minute", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_94", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_117", "standardCode": {"id": "Code_273", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_95", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_118", "standardCode": {"id": "Code_274", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_96", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_119", "standardCode": {"id": "Code_275", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_97", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_120", "standardCode": {"id": "Code_276", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_114", "standardCode": {"id": "Code_269", "code": "C49677", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Heart Rate", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}], "bcCategories": [{"id": "BCC1", "childIds": ["BCC2"], "name": "Blood pressure", "label": "name", "description": "bcCategoryDesc", "memberIds": ["BiomedicalConcept_20"], "code": {"id": "aliasId", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptCategory"}, {"id": "BCC2", "childIds": ["BCC1", "BCC3"], "name": "Blood Pressure Tests", "label": "name", "description": "bcCatDesc", "memberIds": ["BiomedicalConcept_20", "BiomedicalConcept_21"], "code": {"id": "aliasId", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptCategory"}, {"id": "BCC3", "childIds": ["BCC2"], "name": "Diastolic Blood Pressure", "label": "name", "description": "bcCateDes", "memberIds": ["BiomedicalConcept_22"], "code": {"id": "aliasId", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptCategory"}, {"id": "BCC4", "childIds": ["BCC2"], "name": "Systolic Blood Pressure", "label": "name", "description": "bcCate", "memberIds": ["BiomedicalConcept_23"], "code": {"id": "aliasId", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptCategory"}], "bcSurrogates": [{"id": "BiomedicalConceptSurrogate_1", "name": "HbA1c", "label": "HbA1c", "description": "HbA1c", "reference": "None set", "instanceType": "BiomedicalConceptSurrogate"}], "arms": [{"id": "StudyArm_1", "name": "Placebo", "label": "Placebo", "description": "Placebo", "type": {"id": "Code_122", "code": "C174268", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Placebo Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_123", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}, {"id": "StudyArm_2", "name": "Xanomeline <PERSON>", "label": "Xanomeline <PERSON>", "description": "Active Substance", "type": {"id": "Code_124", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Active Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_125", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}, {"id": "StudyArm_3", "name": "Xanomeline High Dose", "label": "Xanomeline High Dose", "description": "Active Substance", "type": {"id": "Code_126", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Active Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_127", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}], "epochs": [{"id": "StudyEpoch_1", "name": "Screening", "label": "Screening", "description": "Screening Epoch", "type": {"id": "Code_128", "code": "C48262", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Screening", "instanceType": "Code"}, "nextId": "StudyEpoch_2", "previousId": null, "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_2", "name": "Treatment 1", "label": "Treatment One", "description": "Treatment Epoch", "type": {"id": "Code_129", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "nextId": "StudyEpoch_3", "previousId": "StudyEpoch_1", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_3", "name": "Treatment 2", "label": "Treatment Two", "description": "Treatment Epoch", "type": {"id": "Code_130", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "nextId": "StudyEpoch_4", "previousId": "StudyEpoch_2", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_4", "name": "Treatment 3", "label": "Treatment Three", "description": "Treatment Epoch", "type": {"id": "Code_131", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "nextId": "StudyEpoch_5", "previousId": "StudyEpoch_3", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_5", "name": "Follow-Up", "label": "Follow Up", "description": "Follow-up Epoch", "type": {"id": "Code_132", "code": "C99158", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinical Study Follow-up", "instanceType": "Code"}, "nextId": null, "previousId": "StudyEpoch_4", "instanceType": "StudyEpoch"}], "elements": [{"id": "StudyElement_1", "name": "EL1", "label": "Screening", "description": "Screening Element", "transitionStartRule": {"id": "TransitionRule_7", "name": "ELEMENT_START_RULE_1", "label": null, "description": null, "text": "Informed consent", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_8", "name": "ELEMENT_END_RULE_1", "label": null, "description": null, "text": "Completion of all screening activities and no more than 2 weeks from informed consent", "instanceType": "TransitionRule"}, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_2", "name": "EL2", "label": "Placebo", "description": "Placebo TTS (adhesive patches)", "transitionStartRule": {"id": "TransitionRule_9", "name": "ELEMENT_START_RULE_2", "label": null, "description": null, "text": "Administration of first dose", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_7", "name": "EL7", "label": "Follow up", "description": "Follow Up Element", "transitionStartRule": {"id": "TransitionRule_14", "name": "ELEMENT_START_RULE_7", "label": null, "description": null, "text": "End of last scheduled visit on study (including early termination)", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_15", "name": "ELEMENT_END_RULE_7", "label": null, "description": null, "text": "Completion of all specified followup activities (which vary on a patient-by-patient basis)", "instanceType": "TransitionRule"}, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_3", "name": "EL3", "label": "Low", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_10", "name": "ELEMENT_START_RULE_3", "label": null, "description": null, "text": "Administration of first dose", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_4", "name": "EL4", "label": "High - Start", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_11", "name": "ELEMENT_START_RULE_4", "label": null, "description": null, "text": "Randomized", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_5", "name": "EL5", "label": "High - Middle", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg + 25 cm2, 27 mg", "transitionStartRule": {"id": "TransitionRule_12", "name": "ELEMENT_START_RULE_5", "label": null, "description": null, "text": "Administration of first dose (from patches supplied at Visit 4)", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_6", "name": "EL6", "label": "High - End", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_13", "name": "ELEMENT_START_RULE_6", "label": null, "description": null, "text": "Administration of first dose (from patches supplied at Visit 12)", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}], "documentVersionId": null, "dictionaries": [{"id": "SyntaxTemplateDictionary_1", "name": "IE_Dict", "label": "IE Dictionary", "description": "Dictionary for IE", "text": null, "parameterMaps": [{"id": "ParameterMap_1", "tag": "min_age", "reference": "<usdm:ref klass=\"Range\" id=\"Range_3\" attribute=\"minValue\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_2", "tag": "max_age", "reference": "<usdm:ref klass=\"Range\" id=\"Range_3\" attribute=\"maxValue\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_3", "tag": "StudyPopulation", "reference": "<usdm:ref klass=\"StudyDesignPopulation\" id=\"StudyDesignPopulation_1\" attribute=\"description\"></usdm:ref>", "instanceType": "ParameterMap"}], "instanceType": "SyntaxTemplateDictionary"}, {"id": "SyntaxTemplateDictionary_2", "name": "AS_Dict", "label": "Assessment Dictionary", "description": "Dictionary for Study Assessments", "text": null, "parameterMaps": [{"id": "ParameterMap_4", "tag": "Activity1", "reference": "<usdm:ref klass=\"Activity\" id=\"Activity_6\" attribute=\"label\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_5", "tag": "Activity2", "reference": "<usdm:ref klass=\"Activity\" id=\"Activity_5\" attribute=\"label\"></usdm:ref>", "instanceType": "ParameterMap"}], "instanceType": "SyntaxTemplateDictionary"}], "maskingRoles": [{"id": "Masking_1", "description": "This is the sponsor masking description", "role": {"id": "Code_141", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Sponsor", "instanceType": "Code"}, "instanceType": "Masking"}], "conditions": [{"contextIds": ["Activity_4"], "appliesToIds": ["Procedure_1"], "id": "Condition_1", "name": "COND1", "label": "X", "description": "Condition for X", "text": "If this is true", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "Condition"}], "organizations": [{"manages": [{"id": "StudySite_1", "name": "SITE_1", "label": "Site One", "description": "Main Site", "currentEnrollment": null, "instanceType": "StudySite"}], "id": "ResearchOrganization_1", "name": "SITE_ORG_1", "label": "Site Org", "identifier": "*********", "identifierScheme": "DUNS", "organizationType": {"id": "Code_349", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinical Study Sponsor", "instanceType": "Code"}, "legalAddress": {"id": "Address_2", "text": "line, city, district, state, postal_code, United Kingdom of Great Britain and Northern Ireland", "line": "line", "city": "city", "district": "district", "state": "state", "postalCode": "postal_code", "country": {"id": "Code_350", "code": "GBR", "codeSystem": "ISO 3166 1 alpha3", "codeSystemVersion": "2020-08", "decode": "United Kingdom of Great Britain and Northern Ireland", "instanceType": "Code"}, "instanceType": "Address"}, "instanceType": "ResearchOrganization"}], "instanceType": "StudyDesign"}], "links": {"studyDefinitions": "/v4/studydefinitions/fa3350ea-9326-4cbe-9fd4-980d0747066e?sdruploadversion=1", "revisionHistory": "/studydefinitions/fa3350ea-9326-4cbe-9fd4-980d0747066e/revisionhistory", "studyDesigns": [{"studyDesignId": "StudyDesign_1", "studyDesignLink": "/v4/studydesigns?studyid=fa3350ea-9326-4cbe-9fd4-980d0747066e&sdruploadversion=1&studydesignid=StudyDesign_1"}]}}