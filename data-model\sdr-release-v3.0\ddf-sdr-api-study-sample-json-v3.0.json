{"study": {"id": "", "name": "studyName", "description": "studyDescription", "label": "studyLabel", "instanceType": "Study", "versions": [{"id": "StudyVersion_1", "versionIdentifier": "2", "rationale": "The discontinuation rate associated with this oral dosing regimen was 58.6% in previous studies, and alternative clinical strategies have been sought to improve tolerance for the compound. To that end, development of a Transdermal Therapeutic System (TTS) has been initiated.", "titles": [{"id": "StudyTitle_1", "text": "LZZT", "type": {"id": "Code_3", "code": "C94108", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Study Acronym", "instanceType": "Code"}, "instanceType": "StudyTitle"}, {"id": "StudyTitle_2", "text": "Xanomeline (LY246708)", "type": {"id": "Code_5", "code": "C99905x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Brief Study Title", "instanceType": "Code"}, "instanceType": "StudyTitle"}, {"id": "StudyTitle_3", "text": "Safety and Efficacy of the Xanomeline Transdermal Therapeutic System (TTS) in Patients with Mild to Moderate Alzheimer's Disease", "type": {"id": "Code_6", "code": "C99905x2", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Official Study Title", "instanceType": "Code"}, "instanceType": "StudyTitle"}, {"id": "StudyTitle_4", "text": "Safety and Efficacy of the Xanomeline Transdermal Therapeutic System (TTS) in Patients with Mild to Moderate Alzheimer's Disease", "type": {"id": "Code_7", "code": "C99905x3", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Public Study Title", "instanceType": "Code"}, "instanceType": "StudyTitle"}], "studyType": {"id": "Code_1", "code": "C98388", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Interventional", "instanceType": "Code"}, "studyPhase": {"id": "AliasCode_1", "standardCode": {"id": "Code_2", "code": "C15601", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Phase II Trial", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "documentVersionId": "StudyProtocolDocumentVersion_1", "dateValues": [{"id": "GovernanceDate_1", "name": "D_APPROVE", "label": "Design Approval", "description": "Design approval date", "type": {"id": "Code_9", "code": "C132352", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Sponsor Approval Date", "instanceType": "Code"}, "dateValue": "2006-06-01", "geographicScopes": [{"id": "GeographicScope_1", "type": {"id": "Code_10", "code": "C68846", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Global", "instanceType": "Code"}, "code": {"id": "AliasCode_1", "standardCode": {"id": "Code_2", "code": "C15601", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Phase II Trial", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "GeographicScope"}], "instanceType": "GovernanceDate"}], "amendments": [{"id": "StudyAmendment_1", "number": "1", "summary": "Updated inclusion criteria", "substantialImpact": true, "primaryReason": {"id": "StudyAmendmentReason_1", "code": {"id": "Code_72", "code": "C99904x3", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "IRB/IEC Feedback", "instanceType": "Code"}, "otherReason": null, "instanceType": "StudyAmendmentReason"}, "secondaryReasons": [{"id": "StudyAmendmentReason_2", "code": {"id": "Code_73", "code": "C17649", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Other", "instanceType": "Code"}, "otherReason": "Fix typographical errors", "instanceType": "StudyAmendmentReason"}], "enrollments": [{"id": "SubjectEnrollment_1", "type": {"id": "Code_75", "code": "C41129", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Region", "instanceType": "Code"}, "code": {"id": "AliasCode_3", "standardCode": {"id": "Code_74", "code": "150", "codeSystem": "ISO 3166 1 alpha3", "codeSystemVersion": "2020-08", "decode": "Europe", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "SubjectEnrollment", "quantity": {"id": "Quantity_1", "value": 15, "unit": null, "instanceType": "Quantity"}}], "previousId": null, "instanceType": "StudyAmendment"}], "businessTherapeuticAreas": [{"id": "Code_4", "code": "PHARMA", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "<PERSON>", "instanceType": "Code"}], "studyIdentifiers": [{"id": "StudyIdentifier_1", "studyIdentifier": "H2Q-MC-LZZT", "studyIdentifierScope": {"id": "Organization_1", "name": "<PERSON>", "label": "", "organizationType": {"id": "Code_76", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinical Study Sponsor", "instanceType": "Code"}, "identifierScheme": "DUNS", "identifier": "00-642-1325", "legalAddress": {"id": "Address_1", "text": "Lilly Corporate Ctr,  Indianapolis  IN  4628 Denmark", "line": "Lilly Corporate Ctr", "city": " Indianapolis ", "district": "", "state": " IN ", "postalCode": " 4628 ", "country": {"id": "Code_77", "code": "DNK", "codeSystem": "ISO 3166 1 alpha3", "codeSystemVersion": "2020-08", "decode": "Denmark", "instanceType": "Code"}, "instanceType": "Address"}, "instanceType": "Organization"}, "instanceType": "StudyIdentifier"}, {"id": "StudyIdentifier_2", "studyIdentifier": "NCT12345678", "studyIdentifierScope": {"id": "Organization_2", "name": "ClinicalTrials.gov", "label": "", "organizationType": {"id": "Code_78", "code": "C93453", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Study Registry", "instanceType": "Code"}, "identifierScheme": "USGOV", "identifier": "CT-GOV", "legalAddress": null, "instanceType": "Organization"}, "instanceType": "StudyIdentifier"}], "studyDesigns": [{"id": "StudyDesign_1", "name": "Study Design 1", "label": "", "instanceType": "StudyDesign", "description": "The main design for the study", "trialIntentTypes": [{"id": "Code_136", "code": "C49656", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Study", "instanceType": "Code"}], "trialTypes": [{"id": "Code_137", "code": "C49666", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Efficacy Study", "instanceType": "Code"}, {"id": "Code_138", "code": "C49667", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Safety Study", "instanceType": "Code"}, {"id": "Code_139", "code": "C49663", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pharmacokinetic Study", "instanceType": "Code"}], "therapeuticAreas": [{"id": "Code_133", "code": "MILD_MOD_ALZ", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Mild to Moderate Alzheimer's Disease", "instanceType": "Code"}, {"id": "Code_134", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer's disease", "instanceType": "Code"}], "characteristics": [{"id": "Code_142", "code": "C99907x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "EXTENSION", "instanceType": "Code"}, {"id": "Code_143", "code": "C98704", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "ADAPTIVE", "instanceType": "Code"}], "interventionModel": {"id": "Code_140", "code": "C82639", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Parallel Study", "instanceType": "Code"}, "encounters": [{"id": "Encounter_1", "name": "E1", "label": "Screening 1", "description": "Screening encounter", "type": {"id": "Code_82", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": null, "nextId": "Encounter_2", "scheduledAtId": null, "environmentalSetting": [{"id": "Code_83", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_84", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": {"id": "TransitionRule_1", "name": "ENCOUNTER_START_RULE_1", "label": null, "description": null, "text": "Subject identifier", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_2", "name": "ENCOUNTER_START_RULE_1", "label": null, "description": null, "text": "completion of screening activities", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_2", "name": "E2", "label": "Screening 2", "description": "Screening encounter - Ambulatory ECG Placement", "type": {"id": "Code_85", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_1", "nextId": "Encounter_3", "scheduledAtId": "Timing_2", "environmentalSetting": [{"id": "Code_86", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_87", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": {"id": "TransitionRule_3", "name": "ENCOUNTER_START_RULE_2", "label": null, "description": null, "text": "subject leaves clinic after connection of ambulatory ECG machine", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_3", "name": "E3", "label": "Baseline", "description": "Baseline encounter - Ambulatory ECG Removal", "type": {"id": "Code_88", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_2", "nextId": "Encounter_4", "scheduledAtId": null, "environmentalSetting": [{"id": "Code_89", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_90", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": {"id": "TransitionRule_4", "name": "ENCOUNTER_START_RULE_3", "label": null, "description": null, "text": "subject has connection of ambulatory ECG machine removed", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_5", "name": "ENCOUNTER_START_RULE_3", "label": null, "description": null, "text": "Radomized", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}, {"id": "Encounter_4", "name": "E4", "label": "Week 2", "description": "Day 14", "type": {"id": "Code_91", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_3", "nextId": "Encounter_5", "scheduledAtId": "Timing_4", "environmentalSetting": [{"id": "Code_92", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_93", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_5", "name": "E5", "label": "Week 4", "description": "Day 28", "type": {"id": "Code_94", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_4", "nextId": "Encounter_6", "scheduledAtId": "Timing_5", "environmentalSetting": [{"id": "Code_95", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_96", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_6", "name": "E7", "label": "Week 6", "description": "Day 42", "type": {"id": "Code_97", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_5", "nextId": "Encounter_7", "scheduledAtId": "Timing_6", "environmentalSetting": [{"id": "Code_98", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_99", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_7", "name": "E8", "label": "Week 8", "description": "Day 56", "type": {"id": "Code_100", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_6", "nextId": "Encounter_8", "scheduledAtId": "Timing_7", "environmentalSetting": [{"id": "Code_101", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_102", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_103", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_8", "name": "E9", "label": "Week 12", "description": "Day 84", "type": {"id": "Code_104", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_7", "nextId": "Encounter_9", "scheduledAtId": "Timing_9", "environmentalSetting": [{"id": "Code_105", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_106", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_107", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_9", "name": "E10", "label": "Week 16", "description": "Day 112", "type": {"id": "Code_108", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_8", "nextId": "Encounter_10", "scheduledAtId": "Timing_11", "environmentalSetting": [{"id": "Code_109", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_110", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_111", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_10", "name": "E11", "label": "Week 20", "description": "Day 140", "type": {"id": "Code_112", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_9", "nextId": "Encounter_11", "scheduledAtId": "Timing_13", "environmentalSetting": [{"id": "Code_113", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_114", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}, {"id": "Code_115", "code": "C171537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Telephone Call", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_11", "name": "E12", "label": "Week 24", "description": "Day 168", "type": {"id": "Code_116", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_10", "nextId": "Encounter_12", "scheduledAtId": "Timing_15", "environmentalSetting": [{"id": "Code_117", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_118", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": null, "instanceType": "Encounter"}, {"id": "Encounter_12", "name": "E13", "label": "Week 26", "description": "Day 182", "type": {"id": "Code_119", "code": "C25716", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Visit", "instanceType": "Code"}, "previousId": "Encounter_11", "nextId": null, "scheduledAtId": "Timing_16", "environmentalSetting": [{"id": "Code_120", "code": "C51282", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinic", "instanceType": "Code"}], "contactModes": [{"id": "Code_121", "code": "C175574", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "In Person", "instanceType": "Code"}], "transitionStartRule": null, "transitionEndRule": {"id": "TransitionRule_6", "name": "ENCOUNTER_START_RULE_12", "label": null, "description": null, "text": "End of treatment", "instanceType": "TransitionRule"}, "instanceType": "Encounter"}], "indications": [{"id": "Indication_1", "name": "IND1", "label": "Alzheimer's disease", "description": "Alzheimer's disease", "codes": [{"id": "Code_330", "code": "G30.9", "codeSystem": "ICD-10-CM", "codeSystemVersion": "1", "decode": "Alzheimer's disease; unspecified", "instanceType": "Code"}], "isRareDisease": false, "instanceType": "Indication"}, {"id": "Indication_2", "name": "IND2", "label": "Alzheimer's disease", "description": "Alzheimer's disease", "codes": [{"id": "Code_331", "code": "26929004", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "Alzheimer's disease", "instanceType": "Code"}], "isRareDisease": false, "instanceType": "Indication"}], "maskingRoles": [{"id": "Masking_1", "description": "This is the sponsor masking description", "role": {"id": "Code_141", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Sponsor", "instanceType": "Code"}, "instanceType": "Masking"}], "studyInterventions": [{"id": "interventionId", "instanceType": "StudyIntervention", "name": "interventionName", "label": "label", "description": "interventionDesc", "codes": [{"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "BAD STUFF", "instanceType": "Code"}], "role": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Role", "instanceType": "Code"}, "type": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "type", "instanceType": "Code"}, "minimumResponseDuration": {"id": "quantityId", "unit": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "value": 25, "instanceType": "Quantity"}, "administrations": [{"id": "adminId", "instanceType": "AgentAdministration", "name": "adminname", "label": "label", "description": "adminDesc", "duration": {"id": "durationId", "quantity": null, "description": "desc", "durationWillVary": true, "reasonDurationWillVary": "reason", "instanceType": "AdministrationDuration"}, "dose": {"id": "doseId", "unit": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "value": 10, "instanceType": "Quantity"}, "route": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "route", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "frequency": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "standardCode": {"id": "Code_2", "code": "C6666x", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "1", "decode": "route", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}}], "productDesignation": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Designation", "instanceType": "Code"}, "pharmacologicClass": {"id": "a387aa89-c025-438e-91dd-b08281d122d0", "code": "C6666x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "<PERSON><PERSON>", "instanceType": "Code"}}], "population": {"id": "StudyDesignPopulation_1", "name": "POP1", "label": "", "description": "Patients with Probable Mild to Moderate Alzheimer's Disease", "includesHealthySubjects": false, "plannedEnrollmentNumber": {"id": "Range_2", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedCompletionNumber": {"id": "Range_1", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedSex": [{"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f614", "code": "C16576", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}, {"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f616", "code": "C20197", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}], "criteria": [{"id": "EligibilityCriterion_1", "name": "Age Criteria", "label": "", "description": "The study age criterion", "text": "Subjects shall be between [min_age] and [max_age]", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion", "category": {"id": "Code_345", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "1", "nextId": null, "previousId": null, "contextId": null}, {"id": "EligibilityCriterion_2", "name": "Pop Criteria", "label": "", "description": "The study population criterion", "text": "[StudyPopulation] as defined by the NINCDS and the ADRDA guidelines (Attachment LZZT.7)", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion", "category": {"id": "Code_346", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "2", "nextId": null, "previousId": null, "contextId": null}, {"id": "EligibilityCriterion_3", "name": "Diag Criteria", "label": "", "description": "The study diagnosis criterion", "text": "[Activity1] score of 10 to 23", "dictionaryId": "SyntaxTemplateDictionary_2", "instanceType": "EligibilityCriterion", "category": {"id": "Code_347", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "3", "nextId": null, "previousId": null, "contextId": null}, {"id": "EligibilityCriterion_4", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion", "category": {"id": "Code_348", "code": "C25370", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Exclusion Criteria", "instanceType": "Code"}, "identifier": "9", "nextId": null, "previousId": null, "contextId": null}], "plannedAge": {"id": "Range_3", "minValue": 50, "maxValue": 100, "unit": {"id": "Code_332", "code": "C29848", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Year", "instanceType": "Code"}, "isApproximate": false, "instanceType": "Range"}, "instanceType": "StudyDesignPopulation", "cohorts": [{"id": "Cohorts_1", "name": "POP1", "label": "", "description": "Patients with Probable Mild to Moderate Alzheimer's Disease", "includesHealthySubjects": false, "plannedEnrollmentNumber": {"id": "Range_2", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedCompletionNumber": {"id": "Range_1", "minValue": 300, "maxValue": 300, "unit": null, "isApproximate": false, "instanceType": "Range"}, "plannedSex": [{"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f614", "code": "C16576", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}, {"id": "61ad4d8f-527c-4f1d-b227-4bbf6141f616", "code": "C20197", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Phase IIa Trial", "instanceType": "Code"}], "criteria": [{"id": "EligibilityCriterion_1", "name": "Age Criteria", "label": "", "description": "The study age criterion", "text": "Subjects shall be between [min_age] and [max_age]", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion", "category": {"id": "Code_345", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "1", "nextId": null, "previousId": null, "contextId": null}, {"id": "EligibilityCriterion_2", "name": "Pop Criteria", "label": "", "description": "The study population criterion", "text": "[StudyPopulation] as defined by the NINCDS and the ADRDA guidelines (Attachment LZZT.7)", "dictionaryId": "SyntaxTemplateDictionary_1", "instanceType": "EligibilityCriterion", "category": {"id": "Code_346", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "2", "nextId": null, "previousId": "EligibilityCriterion_1", "contextId": "StudyVersion_1"}, {"id": "EligibilityCriterion_3", "name": "Diag Criteria", "label": "", "description": "The study diagnosis criterion", "text": "[Activity1] score of 10 to 23", "dictionaryId": "SyntaxTemplateDictionary_2", "instanceType": "EligibilityCriterion", "category": {"id": "Code_347", "code": "C25532", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inclusion Criteria", "instanceType": "Code"}, "identifier": "3", "nextId": "EligibilityCriterion_2", "previousId": null, "contextId": null}, {"id": "EligibilityCriterion_4", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": null, "instanceType": "EligibilityCriterion", "category": {"id": "Code_348", "code": "C25370", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Exclusion Criteria", "instanceType": "Code"}, "identifier": "9", "nextId": null, "previousId": null, "contextId": null}], "plannedAge": {"id": "Range_3", "minValue": 50, "maxValue": 100, "unit": {"id": "Code_332", "code": "C29848", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Year", "instanceType": "Code"}, "isApproximate": false, "instanceType": "Range"}, "instanceType": "StudyCohort", "characteristics": [{"id": "characteristic_1", "name": "Previous Criteria", "label": "", "description": "The previous xanomeline TTS criterion", "text": "Persons who have previously completed or withdrawn from this study or any other study investigating xanomeline TTS or the oral formulation of xanomeline.", "dictionaryId": null, "instanceType": "Characteristic"}]}]}, "objectives": [{"id": "Objective_1", "name": "OBJ1", "label": "", "description": "Main objective", "text": "To determine if there is a statistically significant relationship (overall Type 1 erroralpha=0.05) between the change in both the ADAS-Cog (11) and CIBIC+ scores, and drug dose (0, 50 cm2 [54 mg], and 75 cm2 [81 mg]).", "dictionaryId": null, "instanceType": "Objective", "level": {"id": "Code_335", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Primary Objective", "instanceType": "Code"}, "endpoints": [{"id": "Endpoint_1", "name": "END1", "label": "", "description": "", "text": "Alzheimer's Disease Assessment Scale - Cognitive Subscale, total of 11 items [ADAS-Cog (11)] at Week 24", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_334", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}}, {"id": "Endpoint_2", "name": "END2", "label": "", "description": "", "text": "Video-referenced Clinician’s Interview-based Impression of Change (CIBIC+) at Week 24", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_336", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}}]}, {"id": "Objective_2", "name": "OBJ2", "label": "", "description": "Safety", "text": "To document the safety profile of the xanomeline TTS.", "dictionaryId": null, "instanceType": "Objective", "level": {"id": "Code_338", "code": "C85826", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Primary Objective", "instanceType": "Code"}, "endpoints": [{"id": "Endpoint_3", "name": "END3", "label": "", "description": "", "text": "Adverse events", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_337", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}}, {"id": "Endpoint_4", "name": "END4", "label": "", "description": "", "text": "Vital signs (weight, standing and supine blood pressure, heart rate)", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_339", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}}, {"id": "Endpoint_5", "name": "END5", "label": "", "description": "The change from baseline laboratory value will be  calculated as the difference between the baseline lab value and the endpoint value (i.e., the value at the specified visit) or the end of treatment observation", "text": "Laboratory evaluations (Change from Baseline)", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_340", "code": "C94496", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Primary Endpoint", "instanceType": "Code"}}]}, {"id": "Objective_3", "name": "OBJ3", "label": "", "description": "Behaviour", "text": "To assess the dose-dependent improvement in behavior. Improved scores on the Revised Neuropsychiatric Inventory (NPI-X) will indicate improvement in these\nareas.", "dictionaryId": null, "instanceType": "Objective", "level": {"id": "Code_342", "code": "C85827", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Secondary Objective", "instanceType": "Code"}, "endpoints": [{"id": "Endpoint_6", "name": "END6", "label": "", "description": "", "text": "Alzheimer's Disease Assessment Scale - Cognitive Subscale, total of 11 items [ADAS-Cog (11)] at Weeks 8 and 16", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_341", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}}, {"id": "Endpoint_7", "name": "END7", "label": "", "description": "", "text": "Video-referenced Clinician’s Interview-based Impression of Change (CIBIC+) at Weeks 8 and 16", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_343", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}}, {"id": "Endpoint_8", "name": "END8", "label": "", "description": "", "text": "Mean Revised Neuropsychiatric Inventory (NPI-X) from Week 4 to Week 24", "dictionaryId": null, "instanceType": "Endpoint", "purpose": "", "level": {"id": "Code_344", "code": "C139173", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Secondary Endpoint", "instanceType": "Code"}}]}], "scheduleTimelines": [{"id": "ScheduleTimeline_4", "name": "Main Timeline", "label": "Main Timeline", "description": "This is the main timeline for the study design.", "mainTimeline": true, "entryCondition": "Potential subject identified", "entryId": "ScheduledActivityInstance_9", "exits": [{"id": "ScheduleTimelineExit_4", "instanceType": "ScheduleTimelineExit"}], "timings": [{"id": "Timing_1", "name": "TIM1", "label": "Screening", "description": "Screening timing", "type": {"id": "Code_14", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Before", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 weeks", "relativeToFrom": {"id": "Code_15", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_9", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_2", "name": "TIM2", "label": "Pre dose", "description": "Pre dose timing", "type": {"id": "Code_16", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Before", "instanceType": "Code"}, "value": "P2D", "valueLabel": "2 days", "relativeToFrom": {"id": "Code_17", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_10", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "PT4H", "windowUpper": "PT0H", "windowLabel": "-4..0 hours", "instanceType": "Timing"}, {"id": "Timing_3", "name": "TIM3", "label": "<PERSON><PERSON>", "description": "Dosing anchor", "type": {"id": "Code_19", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "relativeToFrom": {"id": "Code_20", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_11", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_4", "name": "TIM4", "label": "Week 2", "description": "Week 2 timing", "type": {"id": "Code_21", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "relativeToFrom": {"id": "Code_22", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_12", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P3D", "windowUpper": "P3D", "windowLabel": "-3..3 days", "instanceType": "Timing"}, {"id": "Timing_5", "name": "TIM5", "label": "Week 4", "description": "Week 4 timing", "type": {"id": "Code_24", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P4W", "valueLabel": "4 Weeks", "relativeToFrom": {"id": "Code_25", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_13", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P3D", "windowUpper": "P3D", "windowLabel": "-3..3 days", "instanceType": "Timing"}, {"id": "Timing_6", "name": "TIM6", "label": "Week 6", "description": "Week 6 timing", "type": {"id": "Code_27", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P6W", "valueLabel": "6 Weeks", "relativeToFrom": {"id": "Code_28", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_14", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P3D", "windowUpper": "P3D", "windowLabel": "-3..3 days", "instanceType": "Timing"}, {"id": "Timing_7", "name": "TIM7", "label": "Week 8", "description": "Week 8 timing", "type": {"id": "Code_30", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P8W", "valueLabel": "8 Weeks", "relativeToFrom": {"id": "Code_31", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_15", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P3D", "windowUpper": "P3D", "windowLabel": "-3..3 days", "instanceType": "Timing"}, {"id": "Timing_9", "name": "TIM9", "label": "Week 12", "description": "Week 12 timing", "type": {"id": "Code_35", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P12W", "valueLabel": "12 Weeks", "relativeToFrom": {"id": "Code_36", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_17", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P4D", "windowUpper": "P4D", "windowLabel": "-4..4 days", "instanceType": "Timing"}, {"id": "Timing_11", "name": "TIM11", "label": "Week 16", "description": "Week 16 timing", "type": {"id": "Code_40", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P16W", "valueLabel": "16 Weeks", "relativeToFrom": {"id": "Code_41", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_19", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P4D", "windowUpper": "P4D", "windowLabel": "-4..4 days", "instanceType": "Timing"}, {"id": "Timing_13", "name": "TIM13", "label": "Week 20", "description": "Week 20 timing", "type": {"id": "Code_45", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P20W", "valueLabel": "20 Weeks", "relativeToFrom": {"id": "Code_46", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_21", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P4D", "windowUpper": "P4D", "windowLabel": "-4..4 days", "instanceType": "Timing"}, {"id": "Timing_15", "name": "TIM15", "label": "Week 24", "description": "Week 24 timing", "type": {"id": "Code_50", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P24W", "valueLabel": "24 Weeks", "relativeToFrom": {"id": "Code_51", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_23", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P4D", "windowUpper": "P4D", "windowLabel": "-4..4 days", "instanceType": "Timing"}, {"id": "Timing_16", "name": "TIM16", "label": "Week 26", "description": "Week 26 timing", "type": {"id": "Code_53", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P26W", "valueLabel": "26 Weeks", "relativeToFrom": {"id": "Code_54", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_24", "relativeToScheduledInstanceId": "ScheduledActivityInstance_11", "windowLower": "P3D", "windowUpper": "P3D", "windowLabel": "-3..3 days", "instanceType": "Timing"}, {"id": "Timing_8", "name": "TIM8", "label": "Week 8 Home", "description": "Week 8 at home timing", "type": {"id": "Code_33", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "relativeToFrom": {"id": "Code_34", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_16", "relativeToScheduledInstanceId": "ScheduledActivityInstance_15", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_10", "name": "TIM10", "label": "Week 12 Home", "description": "Week 12 at home timing", "type": {"id": "Code_38", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "relativeToFrom": {"id": "Code_39", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_18", "relativeToScheduledInstanceId": "ScheduledActivityInstance_17", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_12", "name": "TIM12", "label": "Week 16 Home", "description": "Week 16 at home timing", "type": {"id": "Code_43", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "relativeToFrom": {"id": "Code_44", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_20", "relativeToScheduledInstanceId": "ScheduledActivityInstance_19", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_14", "name": "TIM14", "label": "Week 20 Home", "description": "Week 20 at home timing", "type": {"id": "Code_48", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "P2W", "valueLabel": "2 Weeks", "relativeToFrom": {"id": "Code_49", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_22", "relativeToScheduledInstanceId": "ScheduledActivityInstance_21", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}], "instances": [{"id": "ScheduledActivityInstance_9", "name": "SCREEN1", "label": "Screen One", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_10", "epochId": "StudyEpoch_1", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_1", "Activity_2", "Activity_3", "Activity_4", "Activity_5", "Activity_6", "Activity_7", "Activity_8", "Activity_9", "Activity_10", "Activity_13", "Activity_16", "Activity_17", "Activity_18", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_24", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "encounterId": "Encounter_1"}, {"id": "ScheduledActivityInstance_10", "name": "SCREEN2", "label": "Screen Two", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_11", "epochId": "StudyEpoch_1", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_14"], "encounterId": "Encounter_2"}, {"id": "ScheduledActivityInstance_11", "name": "DOSE", "label": "<PERSON><PERSON>", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_12", "epochId": "StudyEpoch_2", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_12", "Activity_13", "Activity_15", "Activity_19", "Activity_23", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "encounterId": "Encounter_3"}, {"id": "ScheduledActivityInstance_12", "name": "WK2", "label": "Week 2", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_13", "epochId": "StudyEpoch_2", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_11", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30"], "encounterId": "Encounter_4"}, {"id": "ScheduledActivityInstance_13", "name": "WK4", "label": "Week 4", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_14", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "encounterId": "Encounter_5"}, {"id": "ScheduledActivityInstance_14", "name": "WK6", "label": "Week 6", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_15", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "encounterId": "Encounter_6"}, {"id": "ScheduledActivityInstance_15", "name": "WK8", "label": "Week 8", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_16", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "encounterId": "Encounter_7"}, {"id": "ScheduledActivityInstance_16", "name": "WK8N", "label": "Week NPI", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_17", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_30"], "encounterId": "Encounter_7"}, {"id": "ScheduledActivityInstance_17", "name": "WK12", "label": "Week 12", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_18", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_30"], "encounterId": "Encounter_8"}, {"id": "ScheduledActivityInstance_18", "name": "WK12N", "label": "Week 12 NPI", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_19", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_30"], "encounterId": "Encounter_8"}, {"id": "ScheduledActivityInstance_19", "name": "WK16", "label": "Week 16", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_20", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "encounterId": "Encounter_9"}, {"id": "ScheduledActivityInstance_20", "name": "WK16N", "label": "Week 16 NPI", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_21", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_30"], "encounterId": "Encounter_9"}, {"id": "ScheduledActivityInstance_21", "name": "WK20", "label": "Week 20", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_22", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_23", "Activity_25", "Activity_30"], "encounterId": "Encounter_10"}, {"id": "ScheduledActivityInstance_22", "name": "WK20N", "label": "Week 20 NPI", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_23", "epochId": "StudyEpoch_3", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_30"], "encounterId": "Encounter_10"}, {"id": "ScheduledActivityInstance_23", "name": "WK24", "label": "Week 24", "description": "-", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_24", "epochId": "StudyEpoch_4", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_25", "Activity_27", "Activity_28", "Activity_29", "Activity_30"], "encounterId": "Encounter_11"}, {"id": "ScheduledActivityInstance_24", "name": "WK26", "label": "Week 26", "description": "-", "timelineId": null, "timelineExitId": "ScheduleTimelineExit_4", "defaultConditionId": null, "epochId": "StudyEpoch_5", "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_7", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_25", "Activity_26", "Activity_30"], "encounterId": "Encounter_12"}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_1", "name": "Adverse Event Timeline", "label": "Adverse Event Timeline", "description": "This is the adverse event timeline", "mainTimeline": false, "entryCondition": "Subject suffers an adverse event", "entryId": "ScheduledActivityInstance_1", "exits": [{"id": "ScheduleTimelineExit_1", "instanceType": "ScheduleTimelineExit"}], "timings": [{"id": "Timing_17", "name": "TIM17", "label": "Adverse Event", "description": "Adverse Event", "type": {"id": "Code_56", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "relativeToFrom": {"id": "Code_57", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_1", "relativeToScheduledInstanceId": "ScheduledActivityInstance_1", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}], "instances": [{"id": "ScheduledActivityInstance_1", "name": "AE", "label": "Adevers Event", "description": "-", "timelineId": null, "timelineExitId": "ScheduleTimelineExit_1", "defaultConditionId": null, "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_31"], "encounterId": null}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_2", "name": "Early Termination Timeline", "label": "Early Termination Timeline", "description": "This is the early termination processing", "mainTimeline": false, "entryCondition": "Subject terminates the study early", "entryId": "ScheduledActivityInstance_2", "exits": [{"id": "ScheduleTimelineExit_2", "instanceType": "ScheduleTimelineExit"}], "timings": [{"id": "Timing_18", "name": "TIM18", "label": "Early Termination", "description": "Early Termination", "type": {"id": "Code_58", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "P1D", "valueLabel": "1 Day", "relativeToFrom": {"id": "Code_59", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_2", "relativeToScheduledInstanceId": "ScheduledActivityInstance_2", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}], "instances": [{"id": "ScheduledActivityInstance_2", "name": "ET", "label": "Early Termination", "description": "-", "timelineId": null, "timelineExitId": "ScheduleTimelineExit_2", "defaultConditionId": null, "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_7", "Activity_13", "Activity_16", "Activity_19", "Activity_20", "Activity_21", "Activity_22", "Activity_23", "Activity_25", "Activity_26", "Activity_27", "Activity_28", "Activity_29", "Activity_30", "Activity_32"], "encounterId": null}], "instanceType": "ScheduleTimeline"}, {"id": "ScheduleTimeline_3", "name": "Vital Sign Blood Pressure Timeline", "label": "Vital Sign Blood Pressure Timeline", "description": "BP Profile", "mainTimeline": false, "entryCondition": "Automatic execution", "entryId": "ScheduledActivityInstance_3", "exits": [{"id": "ScheduleTimelineExit_3", "instanceType": "ScheduleTimelineExit"}], "timings": [{"id": "Timing_19", "name": "TIM19", "label": "<PERSON><PERSON>", "description": "<PERSON><PERSON>", "type": {"id": "Code_60", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Fixed Reference", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 mins", "relativeToFrom": {"id": "Code_61", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_3", "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_20", "name": "TIM20", "label": "VS while supine", "description": "VS while supine", "type": {"id": "Code_62", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT5M", "valueLabel": "5 mins", "relativeToFrom": {"id": "Code_63", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_4", "relativeToScheduledInstanceId": "ScheduledActivityInstance_3", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_21", "name": "TIM21", "label": "Standing", "description": "Standing", "type": {"id": "Code_64", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 min", "relativeToFrom": {"id": "Code_65", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "End to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_5", "relativeToScheduledInstanceId": "ScheduledActivityInstance_4", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_22", "name": "TIM22", "label": "VS while standing", "description": "VS while standing", "type": {"id": "Code_66", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT1M", "valueLabel": "1 min", "relativeToFrom": {"id": "Code_67", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_6", "relativeToScheduledInstanceId": "ScheduledActivityInstance_5", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_23", "name": "TIM23", "label": "Standing", "description": "Standing", "type": {"id": "Code_68", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT0M", "valueLabel": "0 min", "relativeToFrom": {"id": "Code_69", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "End to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_7", "relativeToScheduledInstanceId": "ScheduledActivityInstance_6", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}, {"id": "Timing_24", "name": "TIM24", "label": "VS while standing", "description": "VS while standing", "type": {"id": "Code_70", "code": "C201264", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "After", "instanceType": "Code"}, "value": "PT2M", "valueLabel": "2 min", "relativeToFrom": {"id": "Code_71", "code": "C201265", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Start to Start", "instanceType": "Code"}, "relativeFromScheduledInstanceId": "ScheduledActivityInstance_8", "relativeToScheduledInstanceId": "ScheduledActivityInstance_7", "windowLower": null, "windowUpper": null, "windowLabel": "", "instanceType": "Timing"}], "instances": [{"id": "ScheduledActivityInstance_3", "name": "VS_5MIN", "label": "5 minute supine", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_4", "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_33"], "encounterId": null}, {"id": "ScheduledDecisionInstance_1", "name": "NotAvailable", "label": "Dummy-Data", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "", "epochId": null, "instanceType": "ScheduledDecisionInstance", "conditionAssignments": [{"id": "ConditionAssignment_1", "condition": "Dummy-ConditionAssignment", "conditionTargetId": "ScheduledActivityInstance_5", "instanceType": "ConditionAssignment"}]}, {"id": "ScheduledActivityInstance_4", "name": "VS_SUPINE", "label": "Vital signs supine", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_5", "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_34"], "encounterId": null}, {"id": "ScheduledActivityInstance_5", "name": "VS_1MIN", "label": "1 minute standing", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_6", "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_35"], "encounterId": null}, {"id": "ScheduledActivityInstance_6", "name": "VS_STAND1", "label": "Vital signs after 1 min standing", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_7", "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_36"], "encounterId": null}, {"id": "ScheduledActivityInstance_7", "name": "VS_2MIN", "label": "2 minute standing", "description": "", "timelineId": null, "timelineExitId": null, "defaultConditionId": "ScheduledActivityInstance_8", "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_35"], "encounterId": null}, {"id": "ScheduledActivityInstance_8", "name": "VS_STAND3", "label": "<PERSON>l signs after 3 min standing", "description": "", "timelineId": null, "timelineExitId": "ScheduleTimelineExit_3", "defaultConditionId": null, "epochId": null, "instanceType": "ScheduledActivityInstance", "activityIds": ["Activity_36"], "encounterId": null}], "instanceType": "ScheduleTimeline"}], "documentVersionId": null, "dictionaries": [{"id": "SyntaxTemplateDictionary_1", "name": "IE_Dict", "label": "IE Dictionary", "description": "Dictionary for IE", "parameterMaps": [{"id": "ParameterMap_1", "tag": "min_age", "reference": "<usdm:ref klass=\"Range\" id=\"Range_3\" attribute=\"minValue\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_2", "tag": "max_age", "reference": "<usdm:ref klass=\"Range\" id=\"Range_3\" attribute=\"maxValue\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_3", "tag": "StudyPopulation", "reference": "<usdm:ref klass=\"StudyDesignPopulation\" id=\"StudyDesignPopulation_1\" attribute=\"description\"></usdm:ref>", "instanceType": "ParameterMap"}], "instanceType": "SyntaxTemplateDictionary"}, {"id": "SyntaxTemplateDictionary_2", "name": "AS_Dict", "label": "Assessment Dictionary", "description": "Dictionary for Study Assessments", "parameterMaps": [{"id": "ParameterMap_4", "tag": "Activity1", "reference": "<usdm:ref klass=\"Activity\" id=\"Activity_6\" attribute=\"label\"></usdm:ref>", "instanceType": "ParameterMap"}, {"id": "ParameterMap_5", "tag": "Activity2", "reference": "<usdm:ref klass=\"Activity\" id=\"Activity_5\" attribute=\"label\"></usdm:ref>", "instanceType": "ParameterMap"}], "instanceType": "SyntaxTemplateDictionary"}], "conditions": [{"id": "Condition_1", "name": "COND1", "label": "X", "description": "Condition for X", "text": "If this is true", "dictionaryId": "SyntaxTemplateDictionary_1", "contextIds": ["Activity_4"], "appliesToIds": ["Procedure_1"], "instanceType": "Condition"}], "organizations": [{"id": "ResearchOrganization_1", "name": "SITE_ORG_1", "label": "Site Org", "organizationType": {"id": "Code_349", "code": "C70793", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinical Study Sponsor", "instanceType": "Code"}, "identifierScheme": "DUNS", "identifier": "*********", "legalAddress": {"id": "Address_2", "text": "line, city, district, state, postal_code, United Kingdom of Great Britain and Northern Ireland", "line": "line", "city": "city", "district": "district", "state": "state", "postalCode": "postal_code", "country": {"id": "Code_350", "code": "GBR", "codeSystem": "ISO 3166 1 alpha3", "codeSystemVersion": "2020-08", "decode": "United Kingdom of Great Britain and Northern Ireland", "instanceType": "Code"}, "instanceType": "Address"}, "instanceType": "ResearchOrganization", "manages": [{"id": "StudySite_1", "name": "SITE_1", "label": "Site One", "description": "Main Site", "currentEnrollment": null, "instanceType": "StudySite"}]}], "estimands": [{"id": "0bc02031-b058-43f8-9888-0f66f59b825f", "instanceType": "<PERSON><PERSON><PERSON><PERSON>", "interventionId": "interventionId", "summaryMeasure": "TEST", "analysisPopulation": {"id": "77af2751-1707-4edc-a5db-350ebe7233d8", "name": "analysisName", "label": "label", "description": "Population 1", "text": "text", "instanceType": "AnalysisPopulation"}, "variableOfInterestId": "Endpoint_1", "intercurrentEvents": [{"id": "1dc5c1e9-7b21-4455-9f37-ff2fadb8ac65", "name": "Event Name", "label": "name", "description": "Event Description", "strategy": "Strategies", "instanceType": "IntercurrentEvent"}]}], "activities": [{"id": "Activity_1", "name": "Informed consent", "label": "", "description": "", "previousId": null, "nextId": "Activity_2", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_2", "name": "Inclusion/exclusion criteria", "label": "", "description": "", "previousId": "Activity_1", "nextId": "Activity_3", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_3", "name": "Patient number assigned", "label": "", "description": "", "previousId": "Activity_2", "nextId": "Activity_4", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_4", "name": "Demographics", "label": "Demographics", "description": "", "previousId": "Activity_3", "nextId": "Activity_5", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_5", "name": "<PERSON><PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON>", "description": "", "previousId": "Activity_4", "nextId": "Activity_6", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_6", "name": "MMSE", "label": "MMSE", "description": "", "previousId": "Activity_5", "nextId": "Activity_7", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_7", "name": "Physical examination", "label": "", "description": "", "previousId": "Activity_6", "nextId": "Activity_8", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_8", "name": "Medical history", "label": "", "description": "", "previousId": "Activity_7", "nextId": "Activity_9", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_9", "name": "Habits", "label": "", "description": "", "previousId": "Activity_8", "nextId": "Activity_10", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_10", "name": "Chest X-ray", "label": "", "description": "", "previousId": "Activity_9", "nextId": "Activity_11", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_11", "name": "Apo E genotyping", "label": "", "description": "", "previousId": "Activity_10", "nextId": "Activity_12", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_12", "name": "Patient randomised", "label": "", "description": "", "previousId": "Activity_11", "nextId": "Activity_13", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_13", "name": "Vital signs / Temperature", "label": "Vital Signs and Temperature", "description": "", "previousId": "Activity_12", "nextId": "Activity_14", "definedProcedures": [], "biomedicalConceptIds": ["BiomedicalConcept_20", "BiomedicalConcept_21", "BiomedicalConcept_22"], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "ScheduleTimeline_3", "instanceType": "Activity"}, {"id": "Activity_14", "name": "Ambulatory ECG placed", "label": "", "description": "", "previousId": "Activity_13", "nextId": "Activity_15", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_15", "name": "Ambulatory ECG removed", "label": "", "description": "", "previousId": "Activity_14", "nextId": "Activity_16", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_16", "name": "ECG", "label": "", "description": "", "previousId": "Activity_15", "nextId": "Activity_17", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_17", "name": "Placebo TTS test", "label": "", "description": "", "previousId": "Activity_16", "nextId": "Activity_18", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_18", "name": "CT scan", "label": "", "description": "", "previousId": "Activity_17", "nextId": "Activity_19", "definedProcedures": [{"id": "Procedure_1", "name": "PR1", "label": "CT Scan", "description": "CT Scan", "procedureType": "CT Scan", "code": {"id": "Code_79", "code": "383371000119108", "codeSystem": "SNOMED", "codeSystemVersion": "January 31, 2018", "decode": "CT of head without contrast", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_19", "name": "Concomitant medications", "label": "", "description": "", "previousId": "Activity_18", "nextId": "Activity_20", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_20", "name": "Hematology", "label": "", "description": "", "previousId": "Activity_19", "nextId": "Activity_21", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_21", "name": "Chemistry", "label": "", "description": "", "previousId": "Activity_20", "nextId": "Activity_22", "definedProcedures": [], "biomedicalConceptIds": ["BiomedicalConcept_23", "BiomedicalConcept_24", "BiomedicalConcept_25", "BiomedicalConcept_26", "BiomedicalConcept_27", "BiomedicalConcept_28", "BiomedicalConcept_29"], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_22", "name": "Uninalysis", "label": "", "description": "", "previousId": "Activity_21", "nextId": "Activity_23", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_23", "name": "Plasma Specimen (Xanomeline)", "label": "", "description": "", "previousId": "Activity_22", "nextId": "Activity_24", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_24", "name": "Hemoglobin A1C", "label": "", "description": "", "previousId": "Activity_23", "nextId": "Activity_25", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": ["BiomedicalConceptSurrogate_1"], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_25", "name": "Study drug", "label": "Study drug record , Medications dispensed, Medications returned", "description": "", "previousId": "Activity_24", "nextId": "Activity_26", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_26", "name": "TTS Acceptability Survey", "label": "", "description": "", "previousId": "Activity_25", "nextId": "Activity_27", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_27", "name": "ADAS-Cog", "label": "", "description": "", "previousId": "Activity_26", "nextId": "Activity_28", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_28", "name": "CIBIC+", "label": "", "description": "", "previousId": "Activity_27", "nextId": "Activity_29", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_29", "name": "DAD", "label": "", "description": "", "previousId": "Activity_28", "nextId": "Activity_30", "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_30", "name": "NPI-X", "label": "", "description": "", "previousId": "Activity_29", "nextId": null, "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_31", "name": "Adverse events", "label": "", "description": "", "previousId": null, "nextId": null, "definedProcedures": [], "biomedicalConceptIds": ["BiomedicalConcept_1"], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_32", "name": "Check adverse events", "label": "", "description": "", "previousId": "Activity_30", "nextId": null, "definedProcedures": [], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "ScheduleTimeline_1", "instanceType": "Activity"}, {"id": "Activity_33", "name": "<PERSON><PERSON>", "label": "Subject supine", "description": "", "previousId": null, "nextId": "Activity_34", "definedProcedures": [{"id": "Procedure_2", "name": "PR_SUPINE", "label": "", "description": "Subject Supine", "procedureType": "", "code": {"id": "Code_80", "code": "123", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Subject Supine", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_34", "name": "Vital Signs Supine", "label": "Vital signs while supine", "description": "", "previousId": "Activity_33", "nextId": "Activity_35", "definedProcedures": [], "biomedicalConceptIds": ["BiomedicalConcept_14", "BiomedicalConcept_15", "BiomedicalConcept_16"], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_35", "name": "Stand", "label": "Subject Standing", "description": "", "previousId": "Activity_34", "nextId": "Activity_36", "definedProcedures": [{"id": "Procedure_3", "name": "PR_STAND", "label": "", "description": "Subject Standing", "procedureType": "", "code": {"id": "Code_81", "code": "124", "codeSystem": "SPONSOR", "codeSystemVersion": "12", "decode": "Subject Standing", "instanceType": "Code"}, "studyInterventionId": null, "instanceType": "Procedure"}], "biomedicalConceptIds": [], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}, {"id": "Activity_36", "name": "Vital Signs Standing", "label": "Vital signs while standing", "description": "", "previousId": "Activity_35", "nextId": null, "definedProcedures": [], "biomedicalConceptIds": ["BiomedicalConcept_17", "BiomedicalConcept_18", "BiomedicalConcept_19"], "bcCategoryIds": [], "bcSurrogateIds": [], "timelineId": "", "instanceType": "Activity"}], "biomedicalConcepts": [{"id": "BiomedicalConcept_20", "name": "Body Temperature", "label": "Body Temperature", "synonyms": ["Temperature"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C174446", "properties": [{"id": "BiomedicalConceptProperty_98", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_122", "standardCode": {"id": "Code_278", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_99", "name": "Unit of Temperature", "label": "Unit of Temperature", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_18", "isEnabled": true, "code": {"id": "Code_279", "code": "C42537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON><PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_123", "standardCode": {"id": "Code_280", "code": "C44276", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_100", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_124", "standardCode": {"id": "Code_281", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_121", "standardCode": {"id": "Code_277", "code": "C174446", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_21", "name": "Body Weight", "label": "Body Weight", "synonyms": ["Weight"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C81328", "properties": [{"id": "BiomedicalConceptProperty_101", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_126", "standardCode": {"id": "Code_283", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_102", "name": "Unit of Weight", "label": "Unit of Weight", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_19", "isEnabled": true, "code": {"id": "Code_284", "code": "C28252", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Kilogram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_20", "isEnabled": true, "code": {"id": "Code_285", "code": "C48155", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Gram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_21", "isEnabled": true, "code": {"id": "Code_286", "code": "C48531", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pound", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_127", "standardCode": {"id": "Code_287", "code": "C48208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_125", "standardCode": {"id": "Code_282", "code": "C81328", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_22", "name": "Body Height", "label": "Body Height", "synonyms": ["Height"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C164634", "properties": [{"id": "BiomedicalConceptProperty_103", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_129", "standardCode": {"id": "Code_289", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_104", "name": "Unit of Height", "label": "Unit of Height", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_22", "isEnabled": true, "code": {"id": "Code_290", "code": "C49668", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Centimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_23", "isEnabled": true, "code": {"id": "Code_291", "code": "C48500", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inch", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_24", "isEnabled": true, "code": {"id": "Code_292", "code": "C28251", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Millimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_25", "isEnabled": true, "code": {"id": "Code_293", "code": "C41139", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>er", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_130", "standardCode": {"id": "Code_294", "code": "C168688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_128", "standardCode": {"id": "Code_288", "code": "C164634", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_23", "name": "Alanine Aminotransferase Measurement", "label": "Alanine Aminotransferase Measurement", "synonyms": ["ALT", "SGPT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64433", "properties": [{"id": "BiomedicalConceptProperty_105", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_132", "standardCode": {"id": "Code_296", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_106", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_133", "standardCode": {"id": "Code_297", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_107", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_134", "standardCode": {"id": "Code_298", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_108", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_135", "standardCode": {"id": "Code_299", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_131", "standardCode": {"id": "Code_295", "code": "C64433", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alanine Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_24", "name": "Albumin Measurement", "label": "Albumin Measurement", "synonyms": ["Albumin"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64431", "properties": [{"id": "BiomedicalConceptProperty_109", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_137", "standardCode": {"id": "Code_301", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_110", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_138", "standardCode": {"id": "Code_302", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_111", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_139", "standardCode": {"id": "Code_303", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_112", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_140", "standardCode": {"id": "Code_304", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_136", "standardCode": {"id": "Code_300", "code": "C64431", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Albumin Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_25", "name": "Alkaline Phosphatase Measurement", "label": "Alkaline Phosphatase Measurement", "synonyms": ["Alkaline Phosphatase"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64432", "properties": [{"id": "BiomedicalConceptProperty_113", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_142", "standardCode": {"id": "Code_306", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_114", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_143", "standardCode": {"id": "Code_307", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_115", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_144", "standardCode": {"id": "Code_308", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_116", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_145", "standardCode": {"id": "Code_309", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_141", "standardCode": {"id": "Code_305", "code": "C64432", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alkaline Phosphatase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_26", "name": "Aspartate Aminotransferase Measurement", "label": "Aspartate Aminotransferase Measurement", "synonyms": ["AST", "SGOT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64467", "properties": [{"id": "BiomedicalConceptProperty_117", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_147", "standardCode": {"id": "Code_311", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_118", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_148", "standardCode": {"id": "Code_312", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_119", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_149", "standardCode": {"id": "Code_313", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_120", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_150", "standardCode": {"id": "Code_314", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_146", "standardCode": {"id": "Code_310", "code": "C64467", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Aspartate Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_27", "name": "Creatinine Measurement", "label": "Creatinine Measurement", "synonyms": ["Creatinine"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64547", "properties": [{"id": "BiomedicalConceptProperty_121", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_152", "standardCode": {"id": "Code_316", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_122", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_153", "standardCode": {"id": "Code_317", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_123", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_154", "standardCode": {"id": "Code_318", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_124", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_155", "standardCode": {"id": "Code_319", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_151", "standardCode": {"id": "Code_315", "code": "C64547", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Creatinine Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_28", "name": "Potassium Measurement", "label": "Potassium Measurement", "synonyms": ["Potassium", "K"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64853", "properties": [{"id": "BiomedicalConceptProperty_125", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_157", "standardCode": {"id": "Code_321", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_126", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_158", "standardCode": {"id": "Code_322", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_127", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_159", "standardCode": {"id": "Code_323", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_128", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_160", "standardCode": {"id": "Code_324", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_156", "standardCode": {"id": "Code_320", "code": "C64853", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Potassium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_29", "name": "Sodium Measurement", "label": "Sodium Measurement", "synonyms": ["Sodium", "NA"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64809", "properties": [{"id": "BiomedicalConceptProperty_129", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_162", "standardCode": {"id": "Code_326", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_130", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_163", "standardCode": {"id": "Code_327", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_131", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_164", "standardCode": {"id": "Code_328", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_132", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_165", "standardCode": {"id": "Code_329", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_161", "standardCode": {"id": "Code_325", "code": "C64809", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Sodium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_1", "name": "Adverse Event", "label": "Adverse Event", "synonyms": ["Adverse Event Reported Term", "AETERM"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C41331", "properties": [{"id": "BiomedicalConceptProperty_1", "name": "Adverse Event Verbatim Description", "label": "Adverse Event Verbatim Description", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_6", "standardCode": {"id": "Code_145", "code": "C78541", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Verbatim Description", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_2", "name": "Adverse Event Dictionary Derived Term", "label": "Adverse Event Dictionary Derived Term", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_7", "standardCode": {"id": "Code_146", "code": "C83344", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Dictionary Derived Term", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_3", "name": "Adverse Event Category", "label": "Adverse Event Category", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_8", "standardCode": {"id": "Code_147", "code": "C83198", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Category", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_4", "name": "Adverse Event Subcategory", "label": "Adverse Event Subcategory", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_9", "standardCode": {"id": "Code_148", "code": "C83212", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Subcategory", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_5", "name": "Adverse Event Pre-specified", "label": "Adverse Event Pre-specified", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_10", "standardCode": {"id": "Code_149", "code": "C87840", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Pre-specified", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_6", "name": "Severity of Adverse Event", "label": "Severity of Adverse Event", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_11", "standardCode": {"id": "Code_150", "code": "C53253", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Severity of Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_7", "name": "Adverse Event Toxicity Grade", "label": "Adverse Event Toxicity Grade", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_12", "standardCode": {"id": "Code_151", "code": "C78605", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Toxicity Grade", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_8", "name": "Seriousness of Adverse Event", "label": "Seriousness of Adverse Event", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_13", "standardCode": {"id": "Code_152", "code": "C53252", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Seriousness of Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_9", "name": "Adverse Event Action Taken with Study Treatment", "label": "Adverse Event Action Taken with Study Treatment", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_14", "standardCode": {"id": "Code_153", "code": "C83013", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Action Taken with Study Treatment", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_10", "name": "Adverse Event Attribution to Product or Procedure", "label": "Adverse Event Attribution to Product or Procedure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_15", "standardCode": {"id": "Code_154", "code": "C41358", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Attribution to Product or Procedure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_11", "name": "Adverse Event Pattern", "label": "Adverse Event Pattern", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_1", "isEnabled": true, "code": {"id": "Code_155", "code": "C71325", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Intermittent", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_16", "standardCode": {"id": "Code_156", "code": "C83208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Pattern", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_12", "name": "Adverse Event Outcome", "label": "Adverse Event Outcome", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_17", "standardCode": {"id": "Code_157", "code": "C49489", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Outcome", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_13", "name": "Adverse Event Start Date Time", "label": "Adverse Event Start Date Time", "isRequired": true, "isEnabled": true, "datatype": "datetime", "responseCodes": [], "code": {"id": "AliasCode_18", "standardCode": {"id": "Code_158", "code": "C83215", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event Start Date Time", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_14", "name": "Adverse Event End Date Time", "label": "Adverse Event End Date Time", "isRequired": true, "isEnabled": true, "datatype": "datetime", "responseCodes": [], "code": {"id": "AliasCode_19", "standardCode": {"id": "Code_159", "code": "C83201", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event End Date Time", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_5", "standardCode": {"id": "Code_144", "code": "C41331", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Adverse Event", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_2", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_15", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_21", "standardCode": {"id": "Code_161", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_16", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_2", "isEnabled": true, "code": {"id": "Code_162", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_22", "standardCode": {"id": "Code_163", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_17", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_23", "standardCode": {"id": "Code_164", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_18", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_24", "standardCode": {"id": "Code_165", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_19", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_25", "standardCode": {"id": "Code_166", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_20", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_26", "standardCode": {"id": "Code_167", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_20", "standardCode": {"id": "Code_160", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_3", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_21", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_28", "standardCode": {"id": "Code_169", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_22", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_3", "isEnabled": true, "code": {"id": "Code_170", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_29", "standardCode": {"id": "Code_171", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_23", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_30", "standardCode": {"id": "Code_172", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_24", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_31", "standardCode": {"id": "Code_173", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_25", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_32", "standardCode": {"id": "Code_174", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_26", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_33", "standardCode": {"id": "Code_175", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_27", "standardCode": {"id": "Code_168", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_4", "name": "Body Temperature", "label": "Body Temperature", "synonyms": ["Temperature"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C174446", "properties": [{"id": "BiomedicalConceptProperty_27", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_35", "standardCode": {"id": "Code_177", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_28", "name": "Unit of Temperature", "label": "Unit of Temperature", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_4", "isEnabled": true, "code": {"id": "Code_178", "code": "C42537", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON><PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_36", "standardCode": {"id": "Code_179", "code": "C44276", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_29", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_37", "standardCode": {"id": "Code_180", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_34", "standardCode": {"id": "Code_176", "code": "C174446", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Temperature", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_5", "name": "Body Weight", "label": "Body Weight", "synonyms": ["Weight"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C81328", "properties": [{"id": "BiomedicalConceptProperty_30", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_39", "standardCode": {"id": "Code_182", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_31", "name": "Unit of Weight", "label": "Unit of Weight", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_5", "isEnabled": true, "code": {"id": "Code_183", "code": "C28252", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Kilogram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_6", "isEnabled": true, "code": {"id": "Code_184", "code": "C48155", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Gram", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_7", "isEnabled": true, "code": {"id": "Code_185", "code": "C48531", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Pound", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_40", "standardCode": {"id": "Code_186", "code": "C48208", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_38", "standardCode": {"id": "Code_181", "code": "C81328", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Weight", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_6", "name": "Body Height", "label": "Body Height", "synonyms": ["Height"], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C164634", "properties": [{"id": "BiomedicalConceptProperty_32", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_42", "standardCode": {"id": "Code_188", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_33", "name": "Unit of Height", "label": "Unit of Height", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_8", "isEnabled": true, "code": {"id": "Code_189", "code": "C49668", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Centimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_9", "isEnabled": true, "code": {"id": "Code_190", "code": "C48500", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Inch", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_10", "isEnabled": true, "code": {"id": "Code_191", "code": "C28251", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Millimeter", "instanceType": "Code"}, "instanceType": "ResponseCode"}, {"id": "ResponseCode_11", "isEnabled": true, "code": {"id": "Code_192", "code": "C41139", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>er", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_43", "standardCode": {"id": "Code_193", "code": "C168688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_41", "standardCode": {"id": "Code_187", "code": "C164634", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Body Height", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_7", "name": "Alanine Aminotransferase Measurement", "label": "Alanine Aminotransferase Measurement", "synonyms": ["ALT", "SGPT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64433", "properties": [{"id": "BiomedicalConceptProperty_34", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_45", "standardCode": {"id": "Code_195", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_35", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_46", "standardCode": {"id": "Code_196", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_36", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_47", "standardCode": {"id": "Code_197", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_37", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_48", "standardCode": {"id": "Code_198", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_44", "standardCode": {"id": "Code_194", "code": "C64433", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alanine Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_8", "name": "Albumin Measurement", "label": "Albumin Measurement", "synonyms": ["Albumin"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64431", "properties": [{"id": "BiomedicalConceptProperty_38", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_50", "standardCode": {"id": "Code_200", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_39", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_51", "standardCode": {"id": "Code_201", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_40", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_52", "standardCode": {"id": "Code_202", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_41", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_53", "standardCode": {"id": "Code_203", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_49", "standardCode": {"id": "Code_199", "code": "C64431", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Albumin Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_9", "name": "Alkaline Phosphatase Measurement", "label": "Alkaline Phosphatase Measurement", "synonyms": ["Alkaline Phosphatase"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64432", "properties": [{"id": "BiomedicalConceptProperty_42", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_55", "standardCode": {"id": "Code_205", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_43", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_56", "standardCode": {"id": "Code_206", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_44", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_57", "standardCode": {"id": "Code_207", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_45", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_58", "standardCode": {"id": "Code_208", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_54", "standardCode": {"id": "Code_204", "code": "C64432", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Alkaline Phosphatase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_10", "name": "Aspartate Aminotransferase Measurement", "label": "Aspartate Aminotransferase Measurement", "synonyms": ["AST", "SGOT"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64467", "properties": [{"id": "BiomedicalConceptProperty_46", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_60", "standardCode": {"id": "Code_210", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_47", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_61", "standardCode": {"id": "Code_211", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_48", "name": "Unit of Catalytic Activity Concentration", "label": "Unit of Catalytic Activity Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_62", "standardCode": {"id": "Code_212", "code": "C67365", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Catalytic Activity Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_49", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_63", "standardCode": {"id": "Code_213", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_59", "standardCode": {"id": "Code_209", "code": "C64467", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Aspartate Aminotransferase Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_11", "name": "Creatinine Measurement", "label": "Creatinine Measurement", "synonyms": ["Creatinine"], "reference": "/mdr/bc/packages/2023-10-03/biomedicalconcepts/C64547", "properties": [{"id": "BiomedicalConceptProperty_50", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_65", "standardCode": {"id": "Code_215", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_51", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_66", "standardCode": {"id": "Code_216", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_52", "name": "Unit of Concentration", "label": "Unit of Concentration", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_67", "standardCode": {"id": "Code_217", "code": "C48207", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Concentration", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_53", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_68", "standardCode": {"id": "Code_218", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_64", "standardCode": {"id": "Code_214", "code": "C64547", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Creatinine Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_12", "name": "Potassium Measurement", "label": "Potassium Measurement", "synonyms": ["Potassium", "K"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64853", "properties": [{"id": "BiomedicalConceptProperty_54", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_70", "standardCode": {"id": "Code_220", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_55", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_71", "standardCode": {"id": "Code_221", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_56", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_72", "standardCode": {"id": "Code_222", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_57", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_73", "standardCode": {"id": "Code_223", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_69", "standardCode": {"id": "Code_219", "code": "C64853", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Potassium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_13", "name": "Sodium Measurement", "label": "Sodium Measurement", "synonyms": ["Sodium", "NA"], "reference": "/mdr/bc/packages/2023-02-13/biomedicalconcepts/C64809", "properties": [{"id": "BiomedicalConceptProperty_58", "name": "Laboratory Test Result", "label": "Laboratory Test Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_75", "standardCode": {"id": "Code_225", "code": "C36292", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_59", "name": "Laboratory Test Fasting Status", "label": "Laboratory Test Fasting Status", "isRequired": true, "isEnabled": true, "datatype": "boolean", "responseCodes": [], "code": {"id": "AliasCode_76", "standardCode": {"id": "Code_226", "code": "C83309", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Laboratory Test Fasting Status", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_60", "name": "Molarity Unit", "label": "Molarity Unit", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_77", "standardCode": {"id": "Code_227", "code": "C64567", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Molarity Unit", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_61", "name": "Biospecimen Type", "label": "Biospecimen Type", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_78", "standardCode": {"id": "Code_228", "code": "C70713", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Biospecimen Type", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_74", "standardCode": {"id": "Code_224", "code": "C64809", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Sodium Measurement", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_14", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_62", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_80", "standardCode": {"id": "Code_230", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_63", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_12", "isEnabled": true, "code": {"id": "Code_231", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_81", "standardCode": {"id": "Code_232", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_64", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_82", "standardCode": {"id": "Code_233", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_65", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_83", "standardCode": {"id": "Code_234", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_66", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_84", "standardCode": {"id": "Code_235", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_67", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_85", "standardCode": {"id": "Code_236", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_79", "standardCode": {"id": "Code_229", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_15", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_68", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_87", "standardCode": {"id": "Code_238", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_69", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_13", "isEnabled": true, "code": {"id": "Code_239", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_88", "standardCode": {"id": "Code_240", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_70", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_89", "standardCode": {"id": "Code_241", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_71", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_90", "standardCode": {"id": "Code_242", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_72", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_91", "standardCode": {"id": "Code_243", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_73", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_92", "standardCode": {"id": "Code_244", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_86", "standardCode": {"id": "Code_237", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_16", "name": "Heart Rate", "label": "Heart Rate", "synonyms": [], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C49677", "properties": [{"id": "BiomedicalConceptProperty_74", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "integer", "responseCodes": [], "code": {"id": "AliasCode_94", "standardCode": {"id": "Code_246", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_75", "name": "Count per Minute", "label": "Count per Minute", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_14", "isEnabled": true, "code": {"id": "Code_247", "code": "C49673", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Beats per Minute", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_95", "standardCode": {"id": "Code_248", "code": "C73688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Count per Minute", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_76", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_96", "standardCode": {"id": "Code_249", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_77", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_97", "standardCode": {"id": "Code_250", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_78", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_98", "standardCode": {"id": "Code_251", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_79", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_99", "standardCode": {"id": "Code_252", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_93", "standardCode": {"id": "Code_245", "code": "C49677", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Heart Rate", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_17", "name": "Systolic Blood Pressure", "label": "Systolic Blood Pressure", "synonyms": ["SYSBP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25298", "properties": [{"id": "BiomedicalConceptProperty_80", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_101", "standardCode": {"id": "Code_254", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_81", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_15", "isEnabled": true, "code": {"id": "Code_255", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_102", "standardCode": {"id": "Code_256", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_82", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_103", "standardCode": {"id": "Code_257", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_83", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_104", "standardCode": {"id": "Code_258", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_84", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_105", "standardCode": {"id": "Code_259", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_85", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_106", "standardCode": {"id": "Code_260", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_100", "standardCode": {"id": "Code_253", "code": "C25298", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Systolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_18", "name": "Diastolic Blood Pressure", "label": "Diastolic Blood Pressure", "synonyms": ["DIABP"], "reference": "/mdr/bc/packages/2023-07-06/biomedicalconcepts/C25299", "properties": [{"id": "BiomedicalConceptProperty_86", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "decimal", "responseCodes": [], "code": {"id": "AliasCode_108", "standardCode": {"id": "Code_262", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_87", "name": "Unit of Pressure", "label": "Unit of Pressure", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_16", "isEnabled": true, "code": {"id": "Code_263", "code": "C42547", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "<PERSON>", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_109", "standardCode": {"id": "Code_264", "code": "C49669", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Unit of Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_88", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_110", "standardCode": {"id": "Code_265", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_89", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_111", "standardCode": {"id": "Code_266", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_90", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_112", "standardCode": {"id": "Code_267", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_91", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_113", "standardCode": {"id": "Code_268", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_107", "standardCode": {"id": "Code_261", "code": "C25299", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}, {"id": "BiomedicalConcept_19", "name": "Heart Rate", "label": "Heart Rate", "synonyms": [], "reference": "/mdr/bc/packages/2022-10-26/biomedicalconcepts/C49677", "properties": [{"id": "BiomedicalConceptProperty_92", "name": "Vital Signs Result", "label": "Vital Signs Result", "isRequired": true, "isEnabled": true, "datatype": "integer", "responseCodes": [], "code": {"id": "AliasCode_115", "standardCode": {"id": "Code_270", "code": "C173522", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Result", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_93", "name": "Count per Minute", "label": "Count per Minute", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [{"id": "ResponseCode_17", "isEnabled": true, "code": {"id": "Code_271", "code": "C49673", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Beats per Minute", "instanceType": "Code"}, "instanceType": "ResponseCode"}], "code": {"id": "AliasCode_116", "standardCode": {"id": "Code_272", "code": "C73688", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Count per Minute", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_94", "name": "Vital Signs Location", "label": "Vital Signs Location", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_117", "standardCode": {"id": "Code_273", "code": "C83088", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Location", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_95", "name": "Vital Signs Laterality", "label": "Vital Signs Laterality", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_118", "standardCode": {"id": "Code_274", "code": "C123975", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Laterality", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_96", "name": "Test Method", "label": "Test Method", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_119", "standardCode": {"id": "Code_275", "code": "C82535", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Test Method", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}, {"id": "BiomedicalConceptProperty_97", "name": "Vital Signs Position", "label": "Vital Signs Position", "isRequired": true, "isEnabled": true, "datatype": "string", "responseCodes": [], "code": {"id": "AliasCode_120", "standardCode": {"id": "Code_276", "code": "C83114", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Vital Signs Position", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConceptProperty"}], "code": {"id": "AliasCode_114", "standardCode": {"id": "Code_269", "code": "C49677", "codeSystem": "NCI Thesaurus", "codeSystemVersion": "1", "decode": "Heart Rate", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "BiomedicalConcept"}], "blindingSchema": {"id": "AliasCode_4", "standardCode": {"id": "Code_135", "code": "C15228", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Double Blind Study", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "rationale": "The discontinuation rate associated with this oral dosing regimen was 58.6% in previous studies, and alternative clinical strategies have been sought to improve tolerance for the compound. To that end, development of a Transdermal Therapeutic System (TTS) has been initiated.", "epochs": [{"id": "StudyEpoch_1", "name": "Screening", "label": "Screening", "description": "Screening Epoch", "type": {"id": "Code_128", "code": "C48262", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Trial Screening", "instanceType": "Code"}, "previousId": null, "nextId": "StudyEpoch_2", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_2", "name": "Treatment 1", "label": "Treatment One", "description": "Treatment Epoch", "type": {"id": "Code_129", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "previousId": "StudyEpoch_1", "nextId": "StudyEpoch_3", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_3", "name": "Treatment 2", "label": "Treatment Two", "description": "Treatment Epoch", "type": {"id": "Code_130", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "previousId": "StudyEpoch_2", "nextId": "StudyEpoch_4", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_4", "name": "Treatment 3", "label": "Treatment Three", "description": "Treatment Epoch", "type": {"id": "Code_131", "code": "C101526", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Treatment Epoch", "instanceType": "Code"}, "previousId": "StudyEpoch_3", "nextId": "StudyEpoch_5", "instanceType": "StudyEpoch"}, {"id": "StudyEpoch_5", "name": "Follow-Up", "label": "Follow Up", "description": "Follow-up Epoch", "type": {"id": "Code_132", "code": "C99158", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Clinical Study Follow-up", "instanceType": "Code"}, "previousId": "StudyEpoch_4", "nextId": null, "instanceType": "StudyEpoch"}], "elements": [{"id": "StudyElement_1", "name": "EL1", "label": "Screening", "description": "Screening Element", "transitionStartRule": {"id": "TransitionRule_7", "name": "ELEMENT_START_RULE_1", "label": null, "description": null, "text": "Informed consent", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_8", "name": "ELEMENT_END_RULE_1", "label": null, "description": null, "text": "Completion of all screening activities and no more than 2 weeks from informed consent", "instanceType": "TransitionRule"}, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_2", "name": "EL2", "label": "Placebo", "description": "Placebo TTS (adhesive patches)", "transitionStartRule": {"id": "TransitionRule_9", "name": "ELEMENT_START_RULE_2", "label": null, "description": null, "text": "Administration of first dose", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_7", "name": "EL7", "label": "Follow up", "description": "Follow Up Element", "transitionStartRule": {"id": "TransitionRule_14", "name": "ELEMENT_START_RULE_7", "label": null, "description": null, "text": "End of last scheduled visit on study (including early termination)", "instanceType": "TransitionRule"}, "transitionEndRule": {"id": "TransitionRule_15", "name": "ELEMENT_END_RULE_7", "label": null, "description": null, "text": "Completion of all specified followup activities (which vary on a patient-by-patient basis)", "instanceType": "TransitionRule"}, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_3", "name": "EL3", "label": "Low", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_10", "name": "ELEMENT_START_RULE_3", "label": null, "description": null, "text": "Administration of first dose", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_4", "name": "EL4", "label": "High - Start", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_11", "name": "ELEMENT_START_RULE_4", "label": null, "description": null, "text": "Randomized", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_5", "name": "EL5", "label": "High - Middle", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg + 25 cm2, 27 mg", "transitionStartRule": {"id": "TransitionRule_12", "name": "ELEMENT_START_RULE_5", "label": null, "description": null, "text": "Administration of first dose (from patches supplied at Visit 4)", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}, {"id": "StudyElement_6", "name": "EL6", "label": "High - End", "description": "Xanomeline TTS (adhesive patches) 50 cm2, 54 mg", "transitionStartRule": {"id": "TransitionRule_13", "name": "ELEMENT_START_RULE_6", "label": null, "description": null, "text": "Administration of first dose (from patches supplied at Visit 12)", "instanceType": "TransitionRule"}, "transitionEndRule": null, "studyInterventionIds": [], "instanceType": "StudyElement"}], "bcCategories": [{"id": "BCC1", "instanceType": "BiomedicalConceptCategory", "childIds": ["BCC2"], "name": "Blood pressure", "label": "name", "description": "bcCategoryDesc", "memberIds": ["BiomedicalConcept_20"], "code": {"id": "aliasId", "instanceType": "AliasCode", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}]}}, {"id": "BCC2", "instanceType": "BiomedicalConceptCategory", "childIds": ["BCC1", "BCC3"], "name": "Blood Pressure Tests", "label": "name", "description": "bcCatDesc", "memberIds": ["BiomedicalConcept_20", "BiomedicalConcept_21"], "code": {"id": "aliasId", "instanceType": "AliasCode", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}]}}, {"id": "BCC3", "instanceType": "BiomedicalConceptCategory", "childIds": ["BCC2"], "name": "Diastolic Blood Pressure", "label": "name", "description": "bcCateDes", "memberIds": ["BiomedicalConcept_22"], "code": {"id": "aliasId", "instanceType": "AliasCode", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}]}}, {"id": "BCC4", "instanceType": "BiomedicalConceptCategory", "childIds": ["BCC2"], "name": "Systolic Blood Pressure", "label": "name", "description": "bcCate", "memberIds": ["BiomedicalConcept_23"], "code": {"id": "aliasId", "instanceType": "AliasCode", "standardCode": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}, "standardCodeAliases": [{"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "8462-4", "codeSystem": "http://loinc.org/", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure", "instanceType": "Code"}]}}], "bcSurrogates": [{"id": "BiomedicalConceptSurrogate_1", "name": "HbA1c", "label": "HbA1c", "description": "HbA1c", "reference": "None set", "instanceType": "BiomedicalConceptSurrogate"}], "arms": [{"id": "StudyArm_1", "name": "Placebo", "label": "Placebo", "description": "Placebo", "type": {"id": "Code_122", "code": "C174268", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Placebo Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_123", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}, {"id": "StudyArm_2", "name": "Xanomeline <PERSON>", "label": "Xanomeline <PERSON>", "description": "Active Substance", "type": {"id": "Code_124", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Active Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_125", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}, {"id": "StudyArm_3", "name": "Xanomeline High Dose", "label": "Xanomeline High Dose", "description": "Active Substance", "type": {"id": "Code_126", "code": "C174267", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Active Comparator Arm", "instanceType": "Code"}, "dataOriginDescription": "Data collected from subjects", "dataOriginType": {"id": "Code_127", "code": "C188866", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Data Generated Within Study", "instanceType": "Code"}, "populationIds": [], "instanceType": "StudyArm"}], "studyCells": [{"id": "StudyCell_1", "armId": "StudyArm_1", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_2", "armId": "StudyArm_1", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_3", "armId": "StudyArm_1", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_4", "armId": "StudyArm_1", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_2"], "instanceType": "StudyCell"}, {"id": "StudyCell_5", "armId": "StudyArm_1", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}, {"id": "StudyCell_6", "armId": "StudyArm_2", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_7", "armId": "StudyArm_2", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_8", "armId": "StudyArm_2", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_9", "armId": "StudyArm_2", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_3"], "instanceType": "StudyCell"}, {"id": "StudyCell_10", "armId": "StudyArm_2", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}, {"id": "StudyCell_11", "armId": "StudyArm_3", "epochId": "StudyEpoch_1", "elementIds": ["StudyElement_1"], "instanceType": "StudyCell"}, {"id": "StudyCell_12", "armId": "StudyArm_3", "epochId": "StudyEpoch_2", "elementIds": ["StudyElement_4"], "instanceType": "StudyCell"}, {"id": "StudyCell_13", "armId": "StudyArm_3", "epochId": "StudyEpoch_3", "elementIds": ["StudyElement_5"], "instanceType": "StudyCell"}, {"id": "StudyCell_14", "armId": "StudyArm_3", "epochId": "StudyEpoch_4", "elementIds": ["StudyElement_6"], "instanceType": "StudyCell"}, {"id": "StudyCell_15", "armId": "StudyArm_3", "epochId": "StudyEpoch_5", "elementIds": ["StudyElement_7"], "instanceType": "StudyCell"}]}], "instanceType": "StudyVersion"}], "documentedBy": {"id": "StudyProtocolDocument_1", "name": "Protocol_Document_CDISC PILOT - LZZT", "label": null, "description": null, "versions": [{"id": "StudyProtocolDocumentVersion_1", "protocolVersion": "2", "protocolStatus": {"id": "Code_8", "code": "C25508", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Final", "instanceType": "Code"}, "dateValues": [{"id": "GovernanceDate_2", "name": "P_APPROVE", "label": "Protocol Approval", "description": "Protocol document approval date", "type": {"id": "Code_11", "code": "C99903x1", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Protocol Effective Date", "instanceType": "Code"}, "dateValue": "2006-07-01", "geographicScopes": [{"id": "GeographicScope_2", "type": {"id": "Code_13", "code": "C41129", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2023-09-29", "decode": "Region", "instanceType": "Code"}, "code": {"id": "AliasCode_2", "standardCode": {"id": "Code_12", "code": "150", "codeSystem": "ISO 3166 1 alpha3", "codeSystemVersion": "2020-08", "decode": "Europe", "instanceType": "Code"}, "standardCodeAliases": [], "instanceType": "AliasCode"}, "instanceType": "GeographicScope"}], "instanceType": "GovernanceDate"}], "contents": [{"id": "NarrativeContent_1", "name": "ROOT", "sectionNumber": "0", "sectionTitle": "Root", "text": "", "childIds": ["NarrativeContent_2", "NarrativeContent_3", "NarrativeContent_7", "NarrativeContent_10", "NarrativeContent_12", "NarrativeContent_21", "NarrativeContent_32", "NarrativeContent_52", "NarrativeContent_60", "NarrativeContent_94", "NarrativeContent_109", "NarrativeContent_115", "NarrativeContent_119", "NarrativeContent_124", "NarrativeContent_132", "NarrativeContent_133"], "previousId": null, "nextId": "NarrativeContent_2", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_2", "name": "SECTION 0", "sectionNumber": "0", "sectionTitle": "TITLE PAGE", "text": "<div><usdm:section name=\"M11-title-page\"></div>", "childIds": [], "previousId": "NarrativeContent_1", "nextId": "NarrativeContent_3", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_3", "name": "SECTION 1", "sectionNumber": "1", "sectionTitle": "PROTOCOL SUMMARY", "text": "<div></div>", "childIds": ["NarrativeContent_4", "NarrativeContent_5", "NarrativeContent_6"], "previousId": "NarrativeContent_2", "nextId": "NarrativeContent_4", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_4", "name": "SECTION 1.1", "sectionNumber": "1.1", "sectionTitle": "Protocol Synopsis", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_3", "nextId": "NarrativeContent_5", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_5", "name": "SECTION 1.2", "sectionNumber": "1.2", "sectionTitle": "Trial Schema", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_4", "nextId": "NarrativeContent_6", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_6", "name": "SECTION 1.3", "sectionNumber": "1.3", "sectionTitle": "Schedule of Activities", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_5", "nextId": "NarrativeContent_7", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_7", "name": "SECTION 2", "sectionNumber": "2", "sectionTitle": "INTRODUCTION", "text": "<div></div>", "childIds": ["NarrativeContent_8", "NarrativeContent_9"], "previousId": "NarrativeContent_6", "nextId": "NarrativeContent_8", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_8", "name": "SECTION 2.1", "sectionNumber": "2.1", "sectionTitle": "Purpose of Trial", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_7", "nextId": "NarrativeContent_9", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_9", "name": "SECTION 2.2", "sectionNumber": "2.2", "sectionTitle": "Summary of Benefits and Risks", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_8", "nextId": "NarrativeContent_10", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_10", "name": "SECTION 3", "sectionNumber": "3", "sectionTitle": "TRIAL OBJECTIVES, ENDPOINTS AND ESTIMANDS", "text": "<div></div>", "childIds": ["NarrativeContent_11"], "previousId": "NarrativeContent_9", "nextId": "NarrativeContent_11", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_11", "name": "SECTION 3.1", "sectionNumber": "3.1", "sectionTitle": "Primary Objectives", "text": "<div><usdm:section name=\"M11-objective-endpoints\"></div>", "childIds": [], "previousId": "NarrativeContent_10", "nextId": "NarrativeContent_12", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_12", "name": "SECTION 4", "sectionNumber": "4", "sectionTitle": "TRIAL DESIGN", "text": "<div></div>", "childIds": ["NarrativeContent_13", "NarrativeContent_15", "NarrativeContent_19", "NarrativeContent_20"], "previousId": "NarrativeContent_11", "nextId": "NarrativeContent_13", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_13", "name": "SECTION 4.1", "sectionNumber": "4.1", "sectionTitle": "Description of Trial Design", "text": "<div></div>", "childIds": ["NarrativeContent_14"], "previousId": "NarrativeContent_12", "nextId": "NarrativeContent_14", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_14", "name": "SECTION 4.1.1", "sectionNumber": "4.1.1", "sectionTitle": "Participant Input into Design", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_13", "nextId": "NarrativeContent_15", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_15", "name": "SECTION 4.2", "sectionNumber": "4.2", "sectionTitle": "Rationale for Trial Design", "text": "<div><p>Previous studies of the oral formulation have shown that xanomeline tartrate may improve behavior and cognition. Effects on behavior are manifest within 2 to 4 weeks of initiation of treatment. The same studies have shown that 8 to 12 weeks are required to demonstrate effects on cognition and clinical global assessment. This study is intended to determine the acute and chronic effects of the TTS formulation in AD; for that reason, the study is of 26 weeks duration. Dosage specification has been made on the basis of tolerance to the xanomeline TTS in a clinical pharmacology study (H2Q-EW-LKAA), and target plasma levels as determined in studies of the oral formulation of xanomeline (H2Q-MC-LZZA).</p></div>", "childIds": ["NarrativeContent_16", "NarrativeContent_17", "NarrativeContent_18"], "previousId": "NarrativeContent_14", "nextId": "NarrativeContent_16", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_16", "name": "SECTION 4.2.1", "sectionNumber": "4.2.1", "sectionTitle": "Rationale for Comparator", "text": "<div><p>The parallel dosing regimen maximizes the ability to make direct comparisons between the treatment groups. The use of placebo allows for a blinded, thus minimally biased, study. The placebo treatment group is a comparator group for efficacy and safety assessment.</p><p>Two interim analyses are planned for this study. The first interim analysis will occur when 50% of the patients have completed Visit 8 (8 weeks). If required, the second interim analysis will occur when 50% of the patients have completed Visit 12 (24 weeks).</p></div>", "childIds": [], "previousId": "NarrativeContent_15", "nextId": "NarrativeContent_17", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_17", "name": "SECTION 4.2.2", "sectionNumber": "4.2.2", "sectionTitle": "Rationale for Adaptive or Novel Trial Design", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_16", "nextId": "NarrativeContent_18", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_18", "name": "SECTION 4.2.3", "sectionNumber": "4.2.3", "sectionTitle": "Other Trial Design Considerations", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_17", "nextId": "NarrativeContent_19", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_19", "name": "SECTION 4.3", "sectionNumber": "4.3", "sectionTitle": "Access to Trial Intervention After End of Trial", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_18", "nextId": "NarrativeContent_20", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_20", "name": "SECTION 4.4", "sectionNumber": "4.4", "sectionTitle": "Start of Trial and End of Trial", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_19", "nextId": "NarrativeContent_21", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_21", "name": "SECTION 5", "sectionNumber": "5", "sectionTitle": "TRIAL POPULATION", "text": "<div></div>", "childIds": ["NarrativeContent_22", "NarrativeContent_23", "NarrativeContent_24", "NarrativeContent_25", "NarrativeContent_26", "NarrativeContent_31"], "previousId": "NarrativeContent_20", "nextId": "NarrativeContent_22", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_22", "name": "SECTION 5.1", "sectionNumber": "5.1", "sectionTitle": "Selection of Trial Population", "text": "<div><p>For Lilly studies, the following definitions are used:</p>\n<div>\n  <div><strong>Screen</strong></div>\n  <div>\n    <p>Screening is the act of determining if an individual meets minimum requirements to become part of a pool of potential candidates for participation in a clinical study.</p>\n    <p>In this study, <strong>screening</strong> will include asking the candidate preliminary questions (such as age and general health status) and conducting invasive or diagnostic procedures and/or tests (for example, diagnostic psychological tests, x-rays, blood draws). Patients will sign the consent at their screening visit, thereby consenting to undergo the screening procedures and to participate in the study if they qualify.</p>\n  </div>  \n</div>\n<div>\n  <div>To <strong>enter</strong></div>\n  <div>\n    <p>Patients <strong>entered</strong> into the study are those from whom informed consent for the study has been obtained. Adverse events will be reported for each patient who has <strong>entered</strong> the study, even if the patient is never assigned to a treatment group (<strong>enrolled</strong>).</p></div>\n  </div>  \n</div>\n<div>\n  <div>To <strong>enroll</strong></div>\n  <div>\n    <p>Patients who are enrolled in the study are those who have been assigned to a treatment group. Patients who are entered into the study but fail to meet criteria specified in the protocol for treatment assignment will not be enrolled in the study.</p></div>\n  </div>  \n</div>\n<p>At Visit 1, patients who meet the enrollment criteria of Mini-Mental State Examination (MMSE) score of 10 to 23 (Attachment LZZT.6), Hachinski Ischemia Score ≤4 (Attachment LZZT.8), a physical exam, safety labs, ECG, and urinalysis, will proceed to Visit 2 and Visit 3. At Visit 3, patients whose CNS imaging and other pending labs from Visit 1 satisfy the inclusion criteria (Section 3.4.2.1) will be enrolled in the study. Approximately 300 patients with a diagnosis of probable mild to moderate AD will be enrolled in the study.</p></div>", "childIds": [], "previousId": "NarrativeContent_21", "nextId": "NarrativeContent_23", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_23", "name": "SECTION 5.2", "sectionNumber": "5.2", "sectionTitle": "Rationale for Trial Population", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_22", "nextId": "NarrativeContent_24", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_24", "name": "SECTION 5.3", "sectionNumber": "5.3", "sectionTitle": "Inclusion Criteria", "text": "<div><usdm:section name=\"M11-inclusion\"></div>", "childIds": [], "previousId": "NarrativeContent_23", "nextId": "NarrativeContent_25", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_25", "name": "SECTION 5.4", "sectionNumber": "5.4", "sectionTitle": "Exclusion Criteria", "text": "<div><usdm:section name=\"M11-exclusion\"></div>", "childIds": [], "previousId": "NarrativeContent_24", "nextId": "NarrativeContent_26", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_26", "name": "SECTION 5.5", "sectionNumber": "5.5", "sectionTitle": "Lifestyle Considerations", "text": "<div></div>", "childIds": ["NarrativeContent_27", "NarrativeContent_28", "NarrativeContent_29", "NarrativeContent_30"], "previousId": "NarrativeContent_25", "nextId": "NarrativeContent_27", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_27", "name": "SECTION 5.5.1", "sectionNumber": "5.5.1", "sectionTitle": "Meals and Dietary Restrictions", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_26", "nextId": "NarrativeContent_28", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_28", "name": "SECTION 5.5.2", "sectionNumber": "5.5.2", "sectionTitle": "Caffeine, Alcohol, Tobacco, and Other Habits", "text": "<div><p>Not applicable</p></div>", "childIds": [], "previousId": "NarrativeContent_27", "nextId": "NarrativeContent_29", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_29", "name": "SECTION 5.5.3", "sectionNumber": "5.5.3", "sectionTitle": "Physical Activity", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_28", "nextId": "NarrativeContent_30", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_30", "name": "SECTION 5.5.4", "sectionNumber": "5.5.4", "sectionTitle": "Other Activity", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_29", "nextId": "NarrativeContent_31", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_31", "name": "SECTION 5.6", "sectionNumber": "5.6", "sectionTitle": "Screen Failures", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_30", "nextId": "NarrativeContent_32", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_32", "name": "SECTION 6", "sectionNumber": "6", "sectionTitle": "TRIAL INTERVENTION AND CONCOMITANT THERAPY", "text": "<div></div>", "childIds": ["NarrativeContent_33", "NarrativeContent_34", "NarrativeContent_35", "NarrativeContent_37", "NarrativeContent_38", "NarrativeContent_42", "NarrativeContent_46", "NarrativeContent_47"], "previousId": "NarrativeContent_31", "nextId": "NarrativeContent_33", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_33", "name": "SECTION 6.1", "sectionNumber": "6.1", "sectionTitle": "Description of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_32", "nextId": "NarrativeContent_34", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_34", "name": "SECTION 6.2", "sectionNumber": "6.2", "sectionTitle": "Rationale for Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_33", "nextId": "NarrativeContent_35", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_35", "name": "SECTION 6.3", "sectionNumber": "6.3", "sectionTitle": "Dosing and Administration", "text": "<div></div>", "childIds": ["NarrativeContent_36"], "previousId": "NarrativeContent_34", "nextId": "NarrativeContent_36", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_36", "name": "SECTION 6.3.1", "sectionNumber": "6.3.1", "sectionTitle": "Trial Intervention Dose Modification", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_35", "nextId": "NarrativeContent_37", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_37", "name": "SECTION 6.4", "sectionNumber": "6.4", "sectionTitle": "Treatment of Overdose", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_36", "nextId": "NarrativeContent_38", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_38", "name": "SECTION 6.5", "sectionNumber": "6.5", "sectionTitle": "Preparation, Handling, Storage and Accountability", "text": "<div></div>", "childIds": ["NarrativeContent_39", "NarrativeContent_40", "NarrativeContent_41"], "previousId": "NarrativeContent_37", "nextId": "NarrativeContent_39", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_39", "name": "SECTION 6.5.1", "sectionNumber": "6.5.1", "sectionTitle": "Preparation of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_38", "nextId": "NarrativeContent_40", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_40", "name": "SECTION 6.5.2", "sectionNumber": "6.5.2", "sectionTitle": "Handling and Storage of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_39", "nextId": "NarrativeContent_41", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_41", "name": "SECTION 6.5.3", "sectionNumber": "6.5.3", "sectionTitle": "Accountability of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_40", "nextId": "NarrativeContent_42", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_42", "name": "SECTION 6.6", "sectionNumber": "6.6", "sectionTitle": "Participant Assignment, Randomisation and Blinding", "text": "<div></div>", "childIds": ["NarrativeContent_43", "NarrativeContent_44", "NarrativeContent_45"], "previousId": "NarrativeContent_41", "nextId": "NarrativeContent_43", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_43", "name": "SECTION 6.6.1", "sectionNumber": "6.6.1", "sectionTitle": "Participant Assignment", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_42", "nextId": "NarrativeContent_44", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_44", "name": "SECTION 6.6.2", "sectionNumber": "6.6.2", "sectionTitle": "Randomisation", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_43", "nextId": "NarrativeContent_45", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_45", "name": "SECTION 6.6.3", "sectionNumber": "6.6.3", "sectionTitle": "Blinding and Unblinding", "text": "<div><p>The study will be double-blind. To further preserve the blinding of the study, only a minimum number of Lilly and CRO personnel will see the randomization table and codes before the study is complete.</p>\n<p>Emergency codes generated by a computer drug-labeling system will be available to the investigator. These codes, which reveal the patients treatment group, may be opened during the study only if the choice of follow-up treatment depends on the patient’s therapy assignment.</p>\n<p>The investigator should make every effort to contact the clinical research physician prior to unblinding a patient’s therapy assignment. If a patient’s therapy assignment is unblinded, <PERSON> must be notified immediately by telephone. After the study, the investigator must return all sealed and any opened codes.</p></div>", "childIds": [], "previousId": "NarrativeContent_44", "nextId": "NarrativeContent_46", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_46", "name": "SECTION 6.7", "sectionNumber": "6.7", "sectionTitle": "Trial Intervention Compliance", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_45", "nextId": "NarrativeContent_47", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_47", "name": "SECTION 6.8", "sectionNumber": "6.8", "sectionTitle": "Concomitant Therapy", "text": "<div></div>", "childIds": ["NarrativeContent_48", "NarrativeContent_49", "NarrativeContent_50", "NarrativeContent_51"], "previousId": "NarrativeContent_46", "nextId": "NarrativeContent_48", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_48", "name": "SECTION 6.8.1", "sectionNumber": "6.8.1", "sectionTitle": "Prohibited Concomitant Therapy", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_47", "nextId": "NarrativeContent_49", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_49", "name": "SECTION 6.8.2", "sectionNumber": "6.8.2", "sectionTitle": "Permitted Concomitant Therapy", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_48", "nextId": "NarrativeContent_50", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_50", "name": "SECTION 6.8.3", "sectionNumber": "6.8.3", "sectionTitle": "Rescue Therapy", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_49", "nextId": "NarrativeContent_51", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_51", "name": "SECTION 6.8.4", "sectionNumber": "6.8.4", "sectionTitle": "Other Therapy", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_5", "nextId": "NarrativeContent_5", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_52", "name": "SECTION 7", "sectionNumber": "7", "sectionTitle": "DISCONTINUATION OF TRIAL INTERVENTION AND PARTICIPANT WITHDRA<PERSON>L FROM TRIAL", "text": "<div></div>", "childIds": ["NarrativeContent_53", "NarrativeContent_57", "NarrativeContent_58", "NarrativeContent_59"], "previousId": "NarrativeContent_51", "nextId": "NarrativeContent_53", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_53", "name": "SECTION 7.1", "sectionNumber": "7.1", "sectionTitle": "Discontinuation of Trial Intervention", "text": "<div></div>", "childIds": ["NarrativeContent_54", "NarrativeContent_55", "NarrativeContent_56"], "previousId": "NarrativeContent_52", "nextId": "NarrativeContent_54", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_54", "name": "SECTION 7.1.1", "sectionNumber": "7.1.1", "sectionTitle": "Criteria for Permanent Discontinuation of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_53", "nextId": "NarrativeContent_55", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_55", "name": "SECTION 7.1.2", "sectionNumber": "7.1.2", "sectionTitle": "Temporary Discontinuation or Interruption of Trial Intervention", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_54", "nextId": "NarrativeContent_56", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_56", "name": "SECTION 7.1.3", "sectionNumber": "7.1.3", "sectionTitle": "Rechallenge", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_55", "nextId": "NarrativeContent_57", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_57", "name": "SECTION 7.2", "sectionNumber": "7.2", "sectionTitle": "<PERSON>ici<PERSON><PERSON> from the Trial", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_56", "nextId": "NarrativeContent_58", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_58", "name": "SECTION 7.3", "sectionNumber": "7.3", "sectionTitle": "Lost to Follow-Up", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_57", "nextId": "NarrativeContent_59", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_59", "name": "SECTION 7.4", "sectionNumber": "7.4", "sectionTitle": "Trial Stopping Rules", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_58", "nextId": "NarrativeContent_60", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_60", "name": "SECTION 8", "sectionNumber": "8", "sectionTitle": "TRIAL ASSESSMENTS AND PROCEDURES", "text": "<div></div>", "childIds": ["NarrativeContent_61", "NarrativeContent_62", "NarrativeContent_63", "NarrativeContent_69", "NarrativeContent_80", "NarrativeContent_83", "NarrativeContent_89", "NarrativeContent_90", "NarrativeContent_91", "NarrativeContent_92"], "previousId": "NarrativeContent_59", "nextId": "NarrativeContent_61", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_61", "name": "SECTION 8.1", "sectionNumber": "8.1", "sectionTitle": "Screening/Baseline Assessments and Procedures", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_60", "nextId": "NarrativeContent_62", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_62", "name": "SECTION 8.2", "sectionNumber": "8.2", "sectionTitle": "Efficacy Assessments and Procedures", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_61", "nextId": "NarrativeContent_63", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_63", "name": "SECTION 8.3", "sectionNumber": "8.3", "sectionTitle": "Safety Assessments and Procedures", "text": "<div></div>", "childIds": ["NarrativeContent_64", "NarrativeContent_65", "NarrativeContent_66", "NarrativeContent_67", "NarrativeContent_68"], "previousId": "NarrativeContent_62", "nextId": "NarrativeContent_64", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_64", "name": "SECTION 8.3.1", "sectionNumber": "8.3.1", "sectionTitle": "Physical Examination", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_63", "nextId": "NarrativeContent_65", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_65", "name": "SECTION 8.3.2", "sectionNumber": "8.3.2", "sectionTitle": "Vital Signs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_64", "nextId": "NarrativeContent_66", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_66", "name": "SECTION 8.3.3", "sectionNumber": "8.3.3", "sectionTitle": "Electrocardiograms", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_65", "nextId": "NarrativeContent_67", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_67", "name": "SECTION 8.3.4", "sectionNumber": "8.3.4", "sectionTitle": "Clinical Laboratory Assessments", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_66", "nextId": "NarrativeContent_68", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_68", "name": "SECTION 8.3.5", "sectionNumber": "8.3.5", "sectionTitle": "Suicidal Ideation and Behaviour Risk Monitoring", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_67", "nextId": "NarrativeContent_69", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_69", "name": "SECTION 8.4", "sectionNumber": "8.4", "sectionTitle": "Adverse Events and Serious Adverse Events", "text": "<div></div>", "childIds": ["NarrativeContent_70", "NarrativeContent_71", "NarrativeContent_72", "NarrativeContent_73", "NarrativeContent_74", "NarrativeContent_75", "NarrativeContent_76", "NarrativeContent_77", "NarrativeContent_78", "NarrativeContent_79"], "previousId": "NarrativeContent_68", "nextId": "NarrativeContent_70", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_70", "name": "SECTION 8.4.1", "sectionNumber": "8.4.1", "sectionTitle": "Definitions of AE and SAE", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_69", "nextId": "NarrativeContent_71", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_71", "name": "SECTION 8.4.2", "sectionNumber": "8.4.2", "sectionTitle": "Time Period and Frequency for Collecting AE and SAE Information", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_70", "nextId": "NarrativeContent_72", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_72", "name": "SECTION 8.4.3", "sectionNumber": "8.4.3", "sectionTitle": "Identifying AEs and SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_71", "nextId": "NarrativeContent_73", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_73", "name": "SECTION 8.4.4", "sectionNumber": "8.4.4", "sectionTitle": "Recording of AEs and SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_72", "nextId": "NarrativeContent_74", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_74", "name": "SECTION 8.4.5", "sectionNumber": "8.4.5", "sectionTitle": "Follow-up of AEs and SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_73", "nextId": "NarrativeContent_75", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_75", "name": "SECTION 8.4.6", "sectionNumber": "8.4.6", "sectionTitle": "Reporting of SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_74", "nextId": "NarrativeContent_76", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_76", "name": "SECTION 8.4.7", "sectionNumber": "8.4.7", "sectionTitle": "Regulatory Reporting Requirements for SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_75", "nextId": "NarrativeContent_77", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_77", "name": "SECTION 8.4.8", "sectionNumber": "8.4.8", "sectionTitle": "Serious and Unexpected Adverse Reaction Reporting", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_76", "nextId": "NarrativeContent_78", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_78", "name": "SECTION 8.4.9", "sectionNumber": "8.4.9", "sectionTitle": "Adverse Events of Special Interest", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_77", "nextId": "NarrativeContent_79", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_79", "name": "SECTION 8.4.10", "sectionNumber": "8.4.10", "sectionTitle": "Disease-related Events or Outcomes Not Qualifying as AEs or SAEs", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_78", "nextId": "NarrativeContent_80", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_80", "name": "SECTION 8.5", "sectionNumber": "8.5", "sectionTitle": "Pregnancy and Postpartum Information", "text": "<div></div>", "childIds": ["NarrativeContent_81", "NarrativeContent_82"], "previousId": "NarrativeContent_79", "nextId": "NarrativeContent_81", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_81", "name": "SECTION 8.5.1", "sectionNumber": "8.5.1", "sectionTitle": "Participants Who Become Pregnant During the Trial", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_80", "nextId": "NarrativeContent_82", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_82", "name": "SECTION 8.5.2", "sectionNumber": "8.5.2", "sectionTitle": "Participants Whose Partners Become Pregnant", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_81", "nextId": "NarrativeContent_83", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_83", "name": "SECTION 8.6", "sectionNumber": "8.6", "sectionTitle": "Medical Device Product Complaints for Drug/Device Combination Products", "text": "<div></div>", "childIds": ["NarrativeContent_84", "NarrativeContent_85", "NarrativeContent_86", "NarrativeContent_87", "NarrativeContent_88"], "previousId": "NarrativeContent_82", "nextId": "NarrativeContent_84", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_84", "name": "SECTION 8.6.1", "sectionNumber": "8.6.1", "sectionTitle": "Definition of Medical Device Product Complaints", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_83", "nextId": "NarrativeContent_85", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_85", "name": "SECTION 8.6.2", "sectionNumber": "8.6.2", "sectionTitle": "Recording of Medical Device Product Complaints", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_84", "nextId": "NarrativeContent_86", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_86", "name": "SECTION 8.6.3", "sectionNumber": "8.6.3", "sectionTitle": "Time Period and Frequency for Collecting Medical Device Product Complaints .", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_85", "nextId": "NarrativeContent_87", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_87", "name": "SECTION 8.6.4", "sectionNumber": "8.6.4", "sectionTitle": "Follow-Up of Medical Device Product Complaints", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_86", "nextId": "NarrativeContent_88", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_88", "name": "SECTION 8.6.5", "sectionNumber": "8.6.5", "sectionTitle": "Regulatory Reporting Requirements for Medical Device Product Complaints", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_87", "nextId": "NarrativeContent_89", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_89", "name": "SECTION 8.7", "sectionNumber": "8.7", "sectionTitle": "Pharmacokinetics", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_88", "nextId": "NarrativeContent_90", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_90", "name": "SECTION 8.8", "sectionNumber": "8.8", "sectionTitle": "Genetics", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_89", "nextId": "NarrativeContent_91", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_91", "name": "SECTION 8.9", "sectionNumber": "8.9", "sectionTitle": "Biomarkers", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_90", "nextId": "NarrativeContent_92", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_92", "name": "SECTION 8.1", "sectionNumber": "8.1", "sectionTitle": "Immunogenicity Assessments", "text": "<div></div>", "childIds": ["NarrativeContent_93"], "previousId": "NarrativeContent_91", "nextId": "NarrativeContent_93", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_93", "name": "SECTION 8.1.1", "sectionNumber": "8.1.1", "sectionTitle": "Medical Resource Utilisation and Health Economics", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_92", "nextId": "NarrativeContent_94", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_94", "name": "SECTION 9", "sectionNumber": "9", "sectionTitle": "STATISTICAL CONSIDERATIONS", "text": "<div></div>", "childIds": ["NarrativeContent_95", "NarrativeContent_96", "NarrativeContent_102", "NarrativeContent_103", "NarrativeContent_104", "NarrativeContent_105", "NarrativeContent_106", "NarrativeContent_107", "NarrativeContent_108"], "previousId": "NarrativeContent_93", "nextId": "NarrativeContent_95", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_95", "name": "SECTION 9.1", "sectionNumber": "9.1", "sectionTitle": "Analysis Sets", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_94", "nextId": "NarrativeContent_96", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_96", "name": "SECTION 9.2", "sectionNumber": "9.2", "sectionTitle": "Analyses Supporting Primary Objective(s)", "text": "<div></div>", "childIds": ["NarrativeContent_97", "NarrativeContent_98", "NarrativeContent_99", "NarrativeContent_100", "NarrativeContent_101"], "previousId": "NarrativeContent_95", "nextId": "NarrativeContent_97", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_97", "name": "SECTION 9.2.1", "sectionNumber": "9.2.1", "sectionTitle": "Statistical Model, Hypothesis, and Method of Analysis", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_96", "nextId": "NarrativeContent_98", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_98", "name": "SECTION 9.2.2", "sectionNumber": "9.2.2", "sectionTitle": "Handling of Intercurrent Events of Primary Estimand(s)", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_97", "nextId": "NarrativeContent_99", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_99", "name": "SECTION 9.2.3", "sectionNumber": "9.2.3", "sectionTitle": "Handling of Missing Data", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_98", "nextId": "NarrativeContent_100", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_100", "name": "SECTION 9.2.4", "sectionNumber": "9.2.4", "sectionTitle": "Sensitivity Analysis", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_99", "nextId": "NarrativeContent_101", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_101", "name": "SECTION 9.2.5", "sectionNumber": "9.2.5", "sectionTitle": "Supplementary Analysis", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_100", "nextId": "NarrativeContent_102", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_102", "name": "SECTION 9.3", "sectionNumber": "9.3", "sectionTitle": "Analysis Supporting Secondary Objective(s)", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_101", "nextId": "NarrativeContent_103", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_103", "name": "SECTION 9.4", "sectionNumber": "9.4", "sectionTitle": "Analysis of Exploratory Objective(s)", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_102", "nextId": "NarrativeContent_104", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_104", "name": "SECTION 9.5", "sectionNumber": "9.5", "sectionTitle": "Safety Analyses", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_103", "nextId": "NarrativeContent_105", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_105", "name": "SECTION 9.6", "sectionNumber": "9.6", "sectionTitle": "Other Analyses", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_104", "nextId": "NarrativeContent_106", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_106", "name": "SECTION 9.7", "sectionNumber": "9.7", "sectionTitle": "Interim Analyses", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_105", "nextId": "NarrativeContent_107", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_107", "name": "SECTION 9.8", "sectionNumber": "9.8", "sectionTitle": "Sample Size Determination", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_106", "nextId": "NarrativeContent_108", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_108", "name": "SECTION 9.9", "sectionNumber": "9.9", "sectionTitle": "Protocol Deviations", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_107", "nextId": "NarrativeContent_109", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_109", "name": "SECTION 10", "sectionNumber": "10", "sectionTitle": "GENERAL CONSIDERATIONS: R<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ETHIC<PERSON>, AND TRIAL OVERSIGHT", "text": "<div></div>", "childIds": ["NarrativeContent_110", "NarrativeContent_111", "NarrativeContent_112", "NarrativeContent_113", "NarrativeContent_114"], "previousId": "NarrativeContent_108", "nextId": "NarrativeContent_110", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_110", "name": "SECTION 10.1", "sectionNumber": "10.1", "sectionTitle": "Regulatory and Ethical Considerations", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_109", "nextId": "NarrativeContent_111", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_111", "name": "SECTION 10.2", "sectionNumber": "10.2", "sectionTitle": "Committees", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_110", "nextId": "NarrativeContent_112", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_112", "name": "SECTION 10.3", "sectionNumber": "10.3", "sectionTitle": "Informed Consent Process", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_111", "nextId": "NarrativeContent_113", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_113", "name": "SECTION 10.4", "sectionNumber": "10.4", "sectionTitle": "Data Protection", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_112", "nextId": "NarrativeContent_114", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_114", "name": "SECTION 10.5", "sectionNumber": "10.5", "sectionTitle": "Early Site Closure or Trial Termination", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_113", "nextId": "NarrativeContent_115", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_115", "name": "SECTION 11", "sectionNumber": "11", "sectionTitle": "GENERAL CONSIDERATIONS: RISK MANAGEMENT AND QUALITY ASSURANCE", "text": "<div></div>", "childIds": ["NarrativeContent_116", "NarrativeContent_117", "NarrativeContent_118"], "previousId": "NarrativeContent_114", "nextId": "NarrativeContent_116", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_116", "name": "SECTION 11.1", "sectionNumber": "11.1", "sectionTitle": "Quality Tolerance Limits", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_115", "nextId": "NarrativeContent_117", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_117", "name": "SECTION 11.2", "sectionNumber": "11.2", "sectionTitle": "Data Quality Assurance", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_116", "nextId": "NarrativeContent_118", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_118", "name": "SECTION 11.3", "sectionNumber": "11.3", "sectionTitle": "Source Data", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_117", "nextId": "NarrativeContent_119", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_119", "name": "SECTION 12", "sectionNumber": "12", "sectionTitle": "APPENDIX: <PERSON><PERSON><PERSON><PERSON> EVENTS AND SERIOUS ADVERSE EVENTS - DEFINITIONS, SEVERITY, AND CAUSALITY", "text": "<div></div>", "childIds": ["NarrativeContent_120", "NarrativeContent_121", "NarrativeContent_122", "NarrativeContent_123"], "previousId": "NarrativeContent_118", "nextId": "NarrativeContent_120", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_120", "name": "SECTION 12.1", "sectionNumber": "12.1", "sectionTitle": "Further Details and Clarifications on the AE Definition", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_119", "nextId": "NarrativeContent_121", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_121", "name": "SECTION 12.2", "sectionNumber": "12.2", "sectionTitle": "Further Details and Clarifications on the SAE Definition", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_122", "nextId": "NarrativeContent_124", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_122", "name": "SECTION 12.3", "sectionNumber": "12.3", "sectionTitle": "Severity", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_121", "nextId": "NarrativeContent_123", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_123", "name": "SECTION 12.4", "sectionNumber": "12.4", "sectionTitle": "Causality", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_122", "nextId": "NarrativeContent_124", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_124", "name": "SECTION 13", "sectionNumber": "13", "sectionTitle": "APPENDIX: DEFINITIONS AND SUPPORTING OPERATIONAL DETAILS", "text": "<div></div>", "childIds": ["NarrativeContent_125", "NarrativeContent_129", "NarrativeContent_130", "NarrativeContent_131"], "previousId": "NarrativeContent_123", "nextId": "NarrativeContent_125", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_125", "name": "SECTION 13.1", "sectionNumber": "13.1", "sectionTitle": "Contraception and Pregnancy Testing", "text": "<div></div>", "childIds": ["NarrativeContent_126", "NarrativeContent_127", "NarrativeContent_128"], "previousId": "NarrativeContent_124", "nextId": "NarrativeContent_126", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_126", "name": "SECTION 13.1.1", "sectionNumber": "13.1.1", "sectionTitle": "Definitions Related to Childbearing Potential", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_125", "nextId": "NarrativeContent_127", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_127", "name": "SECTION 13.1.2", "sectionNumber": "13.1.2", "sectionTitle": "Contraception", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_126", "nextId": "NarrativeContent_128", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_128", "name": "SECTION 13.1.3", "sectionNumber": "13.1.3", "sectionTitle": "Pregnancy Testing", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_127", "nextId": "NarrativeContent_129", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_129", "name": "SECTION 13.2", "sectionNumber": "13.2", "sectionTitle": "Clinical Laboratory Tests", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_128", "nextId": "NarrativeContent_130", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_130", "name": "SECTION 13.3", "sectionNumber": "13.3", "sectionTitle": "Country/Region-Specific Differences", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_129", "nextId": "NarrativeContent_131", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_131", "name": "SECTION 13.4", "sectionNumber": "13.4", "sectionTitle": "Prior Protocol Amendments", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_130", "nextId": "NarrativeContent_132", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_132", "name": "SECTION 14", "sectionNumber": "14", "sectionTitle": "APPENDIX: GLOSSARY OF TERMS", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_131", "nextId": "NarrativeContent_133", "instanceType": "NarrativeContent"}, {"id": "NarrativeContent_133", "name": "SECTION 15", "sectionNumber": "15", "sectionTitle": "APPENDIX: REFERENCES", "text": "<div></div>", "childIds": [], "previousId": "NarrativeContent_132", "nextId": "", "instanceType": "NarrativeContent"}], "childIds": [], "instanceType": "StudyProtocolDocumentVersion"}], "instanceType": "StudyProtocolDocument"}}, "usdmVersion": "3.0", "systemName": "SDR-RI", "systemVersion": "3.0"}