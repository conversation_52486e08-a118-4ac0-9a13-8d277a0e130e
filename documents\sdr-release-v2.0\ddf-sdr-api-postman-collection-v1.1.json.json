{"info": {"_postman_id": "5c3fcec9-f68e-4d58-86eb-34fa1631fc1e", "name": "Demo", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "V1 API", "item": [{"name": "GetStudy", "request": {"method": "GET", "header": [{"key": "usdmVersion", "value": "1.0", "type": "default"}], "url": {"raw": "{{URL}}/v1/studydefinitions/{studyId}", "host": ["{{URL}}"], "path": ["v1", "studydefinitions", "{studyId}"], "query": [{"key": "sdruploadversion", "value": "1", "disabled": true}]}}, "response": []}, {"name": "GetAuditTrail", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/v1/audittrail/{studyId}", "host": ["{{URL}}"], "path": ["v1", "audittrail", "{studyId}"], "query": [{"key": "fromDate", "value": "2022-03-04", "disabled": true}, {"key": "toDate", "value": "2022-03-08", "disabled": true}]}}, "response": []}, {"name": "GetStudyDesigns", "request": {"method": "GET", "header": [{"key": "usdmVersion", "value": "1.0", "type": "default"}], "url": {"raw": "{{URL}}/v1/studydesigns?study_uuid={studyId}", "host": ["{{URL}}"], "path": ["v1", "studydesigns"], "query": [{"key": "sdruploadversion", "value": "1", "disabled": true}, {"key": "study_uuid", "value": "{studyId}"}]}}, "response": []}, {"name": "GetStudyHistory", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/v1/studydefinitions/history", "host": ["{{URL}}"], "path": ["v1", "studydefinitions", "history"], "query": [{"key": "fromDate", "value": "2022-11-30", "disabled": true}, {"key": "toDate", "value": "2022-03-08", "disabled": true}, {"key": "studyTitle", "value": "Umbrella Study Of Cancer", "disabled": true}]}}, "response": []}, {"name": "POST Study", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "usdmVersion", "value": "1.0", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n\t\"clinicalStudy\": {\r\n\t\t\"uuid\": \"\",\r\n\t\t\"studyTitle\": \"Umbrella Study of Cancer\",\r\n\t\t\"studyType\": {\r\n\t\t\t\"uuid\": \"\",\r\n\t\t\t\"code\": \"C98388\",\r\n\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\"decode\": \"INTERVENTIONAL\"\r\n\t\t},\r\n\t\t\"studyIdentifiers\": [\r\n\t\t\t{\r\n\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\"studyIdentifier\": \"CT-GOV-1234\",\r\n\t\t\t\t\"studyIdentifierScope\": {\r\n\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\"organisationIdentifier\": \"CT-GOV\",\r\n\t\t\t\t\t\"organisationIdentifierScheme\": \"FDA\",\r\n\t\t\t\t\t\"organisationName\": \"ClinicalTrials.gov\",\r\n\t\t\t\t\t\"organisationType\": {\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C2365x\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\"decode\": \"Clinical Study Sponsor\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t{\r\n\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\"studyIdentifier\": \"CT-GOV-1234\",\r\n\t\t\t\t\"studyIdentifierScope\": {\r\n\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\"organisationIdentifier\": \"CT-GOV\",\r\n\t\t\t\t\t\"organisationIdentifierScheme\": \"FDA\",\r\n\t\t\t\t\t\"organisationName\": \"ClinicalTrials.gov\",\r\n\t\t\t\t\t\"organisationType\": {\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C2365x\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\"decode\": \"Clinical Study Registry\"\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t],\r\n\t\t\"studyPhase\": {\r\n\t\t\t\"uuid\": \"\",\r\n\t\t\t\"code\": \"C49686\",\r\n\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\"decode\": \"Phase IIa Trial\"\r\n\t\t},\r\n\t\t\"studyProtocolVersions\": [\r\n\t\t\t{\r\n\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\"briefTitle\": \"Short\",\r\n\t\t\t\t\"officialTitle\": \"Very Official\",\r\n\t\t\t\t\"protocolAmendment\": \"Ammendment\",\r\n\t\t\t\t\"protocolEffectiveDate\": \"2022-01-01\",\r\n\t\t\t\t\"protocolStatus\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C1113x\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\"decode\": \"FINAL 1\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"protocolVersion\": \"1\",\r\n\t\t\t\t\"publicTitle\": \"Public Voice\",\r\n\t\t\t\t\"scientificTitle\": \"Incomprehensible\"\r\n\t\t\t}\r\n\t\t],\r\n\t\t\"studyDesigns\": [\r\n\t\t\t{\r\n\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\"interventionModel\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C82639\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\"decode\": \"Parallel Study\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"trialIntentType\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C15714\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\"decode\": \"Basic Research\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"trialType\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"code\": \"C158288\",\r\n\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\"decode\": \"Biosimilarity Study\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyIndications\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"indicationDesc\": \"Something bad\",\r\n\t\t\t\t\t\t\"codes\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"code\": \"C6666x\",\r\n\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\"decode\": \"BAD STUFF\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyInvestigationalInterventions\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"interventionDesc\": \"Intervention 1\",\r\n\t\t\t\t\t\t\"codes\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"code\": \"C7639x\",\r\n\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\"decode\": \"MODEL 1\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyObjectives\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"objectiveDesc\": \"Objective Level 1\",\r\n\t\t\t\t\t\t\"objectiveLevel\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"code\": \"C9844x\",\r\n\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\"decode\": \"OBJ LEVEL\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\"objectiveEndpoints\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"endpointDesc\": \"Endpoint 1\",\r\n\t\t\t\t\t\t\t\t\"endpointPurposeDesc\": \"level description\",\r\n\t\t\t\t\t\t\t\t\"endpointLevel\": [\r\n\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\"code\": \"C9834x\",\r\n\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\t\"decode\": \"PURPOSE\"\r\n\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyPopulations\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"populationDesc\": \"Population 1\"\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyCells\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"studyArm\": {\r\n\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\"studyArmDataOriginDesc\": \"Captured subject data\",\r\n\t\t\t\t\t\t\t\"studyArmDataOriginType\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"code\": \"C6574y\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\"decode\": \"SUBJECT DATA\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\"studyArmDesc\": \"The Placebo Arm\",\r\n\t\t\t\t\t\t\t\"studyArmName\": \"Placebo\",\r\n\t\t\t\t\t\t\t\"studyArmType\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"code\": \"C174268\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\"decode\": \"Placebo Control Arm\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"studyEpoch\": {\r\n\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\"nextStudyEpochId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58500\",\r\n\t\t\t\t\t\t\t\"previousStudyEpochId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58501\",\r\n\t\t\t\t\t\t\t\"studyEpochDesc\": \"The run in\",\r\n\t\t\t\t\t\t\t\"studyEpochName\": \"Run In\",\r\n\t\t\t\t\t\t\t\"studyEpochType\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"code\": \"C98779\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\"decode\": \"Run-in Period\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\"encounters\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"encounterContactMode\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C1755745\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"In Person\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"encounterDesc\": \"desc\",\r\n\t\t\t\t\t\t\t\t\t\"encounterEnvironmentalSetting\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C51282\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"Clinic\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"encounterName\": \"Encounter 1\",\r\n\t\t\t\t\t\t\t\t\t\"encounterType\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C7652x\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"SITE VISIT\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"transitionStartRule\": {\r\n\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"Start Rule\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"transitionEndRule\": {\r\n\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"End Rule\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"nextEncounterId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58500\",\r\n\t\t\t\t\t\t\t\t\t\"previousEncounterId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58501\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"studyElements\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"studyElementDesc\": \"First element\",\r\n\t\t\t\t\t\t\t\t\"studyElementName\": \"Element 1\",\r\n\t\t\t\t\t\t\t\t\"transitionStartRule\": {\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"Start Rule\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"transitionEndRule\": {\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"End Rule\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyWorkflows\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"workflowDesc\": \"SampleDesc\",\r\n\t\t\t\t\t\t\"workflowItems\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"workflowItemDesc\": \"Sample Item Desc\",\r\n\t\t\t\t\t\t\t\t\"workflowItemActivity\": {\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"activityDesc\": \"Activity_1\",\r\n\t\t\t\t\t\t\t\t\t\"activityName\": \"A1\",\r\n\t\t\t\t\t\t\t\t\t\"definedProcedures\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"procedureCode\": [\r\n\t\t\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"code\": \"767002\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"SNOMED-CT\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-05-31\",\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"White blood cell count\"\r\n\t\t\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\t\t\"procedureType\": \"Specimen Collection\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"nextActivityId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58500\",\r\n\t\t\t\t\t\t\t\t\t\"previousActivityId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58501\",\r\n\t\t\t\t\t\t\t\t\t\"studyDataCollection\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"studyDataName\": \"Study Data 1\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"studyDataDesc\": \"Something\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"crfLink\": \"Link 1\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"workflowItemEncounter\": {\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"encounterContactMode\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C175574\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"In Person\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"encounterDesc\": \"desc\",\r\n\t\t\t\t\t\t\t\t\t\"encounterEnvironmentalSetting\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C51282\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"2022-03-25\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"Clinic\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"encounterName\": \"Encounter 1\",\r\n\t\t\t\t\t\t\t\t\t\"encounterType\": [\r\n\t\t\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"code\": \"C7652x\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\t\t\"decode\": \"SITE VISIT\"\r\n\t\t\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t\t\t],\r\n\t\t\t\t\t\t\t\t\t\"transitionStartRule\": {\r\n\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"Start Rule\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"transitionEndRule\": {\r\n\t\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\t\"transitionRuleDesc\": \"End Rule\"\r\n\t\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\t\"nextEncounterId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58500\",\r\n\t\t\t\t\t\t\t\t\t\"previousEncounterId\": \"c0d6b66e-0831-4f7d-94a2-1e12b6b58501\"\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t\t\"nextWorkflowItemId\": \"\",\r\n\t\t\t\t\t\t\t\t\"previousWorkflowItemId\": \"\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\t\"studyEstimands\": [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\"treatment\": {\r\n\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\"interventionDesc\": \"Intervention 1\",\r\n\t\t\t\t\t\t\t\"codes\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"code\": \"C7639x\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\"decode\": \"MODEL 1\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"summaryMeasure\": \"TEST\",\r\n\t\t\t\t\t\t\"analysisPopulation\": {\r\n\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\"populationDesc\": \"Population 1\"\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"variableOfInterest\": {\r\n\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\"endpointDesc\": \"Endpoint 1\",\r\n\t\t\t\t\t\t\t\"endpointPurposeDesc\": \"level description\",\r\n\t\t\t\t\t\t\t\"endpointLevel\": [\r\n\t\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\t\"code\": \"C9834x\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystem\": \"http:www.cdisc.org\",\r\n\t\t\t\t\t\t\t\t\t\"codeSystemVersion\": \"1\",\r\n\t\t\t\t\t\t\t\t\t\"decode\": \"PURPOSE\"\r\n\t\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t]\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\"intercurrentEvents\": [\r\n\t\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\t\t\"uuid\": \"\",\r\n\t\t\t\t\t\t\t\t\"intercurrentEventDesc\": \"Event 1 Desc\",\r\n\t\t\t\t\t\t\t\t\"intercurrentEventName\": \"Event 1 Name\",\r\n\t\t\t\t\t\t\t\t\"intercurrentEventStrategy\": \"Strategy\"\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t]\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t],\r\n\t\t\"studyVersion\": \"1\"\r\n\t}\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/v1/studydefinitions", "host": ["{{URL}}"], "path": ["v1", "studydefinitions"]}}, "response": []}]}, {"name": "V2 API", "item": [{"name": "GetStudy", "request": {"method": "GET", "header": [{"key": "usdmVersion", "value": "1.9", "type": "default"}], "url": {"raw": "{{URL}}/v2/studydefinitions/{studyId}", "host": ["{{URL}}"], "path": ["v2", "studydefinitions", "{studyId}"], "query": [{"key": "sdruploadversion", "value": "3", "disabled": true}]}}, "response": []}, {"name": "GetStudyDesigns", "request": {"method": "GET", "header": [{"key": "usdmVersion", "value": "1.9", "type": "default"}], "url": {"raw": "{{URL}}/v2/studydesigns?study_uuid={studyId}", "host": ["{{URL}}"], "path": ["v2", "studydesigns"], "query": [{"key": "sdruploadversion", "value": "1", "disabled": true}, {"key": "study_uuid", "value": "{studyId}"}]}}, "response": []}, {"name": "POST Study", "event": [{"listen": "test", "script": {"exec": [""], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "usdmVersion", "value": "1.9", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n    \"clinicalStudy\": {        \r\n        \"studyTitle\": \"Study of Stage III Biliary Duct Cancer\",\r\n        \"studyVersion\": \"1\",\r\n        \"studyType\": {\r\n            \"codeId\": \"7bbb6fd9-7e43-4dbf-90fa-3de117dc76d2\",\r\n            \"code\": \"C98388\",\r\n            \"codeSystem\": \"http:www.cdisc.org\",\r\n            \"codeSystemVersion\": \"2022-03-24\",\r\n            \"decode\": \"INTERVENTIONAL\"\r\n        },\r\n        \"studyRationale\": \"Rational\",\r\n        \"studyAcronym\": \"Acronym\",\r\n        \"studyIdentifiers\": [\r\n            {\r\n                \"studyIdentifierId\": \"c4663375-29be-4b37-9a7d-b2ef927fdf64\",\r\n                \"studyIdentifier\": \"CT-GOV-1234\",\r\n                \"studyIdentifierScope\": {\r\n                    \"organisationId\": \"96dd6b83-2d34-45ac-9d08-3e647b2c7316\",\r\n                    \"organisationIdentifier\": \"CT-GOV\",\r\n                    \"organisationIdentifierScheme\": \"FDA\",\r\n                    \"organisationName\": \"ClinicalTrials.gov\",\r\n                    \"organisationType\": {\r\n                        \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                        \"code\": \"C2365x\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"1\",\r\n                        \"decode\": \"Clinical Study Sponsor\"\r\n                    },\r\n                    \"organizationLegalAddress\": {\r\n                        \"text\": \"text\",\r\n                        \"line\": \" line2\",\r\n                        \"city\": \" city\",\r\n                        \"district\": \"district \",\r\n                        \"state\": \"state \",\r\n                        \"postalCode\": \" postalCode\",\r\n                        \"country\": {\r\n                            \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                            \"code\": \"C2365x\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Egypt\"\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            {\r\n                \"studyIdentifierId\": \"c3f0bac9-72e4-4df6-98ed-e364d10c8239\",\r\n                \"studyIdentifier\": \"CT-GOV\",\r\n                \"studyIdentifierScope\": {\r\n                    \"organisationId\": \"502db4f4-3573-444c-b0a1-ac677e46556a\",\r\n                    \"organisationIdentifier\": \"CT-GOV\",\r\n                    \"organisationIdentifierScheme\": \"FDA\",\r\n                    \"organisationName\": \"ClinicalTrials.gov\",\r\n                    \"organisationType\": {\r\n                        \"codeId\": \"d036ab0b-ce9c-4857-838f-07888180d161\",\r\n                        \"code\": \"C2365x\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"1\",\r\n                        \"decode\": \"Regulatory Agency\"\r\n                    },\r\n                    \"organizationLegalAddress\": {\r\n                        \"text\": \"text\",\r\n                        \"line\": \" line2\",\r\n                        \"city\": \" city\",\r\n                        \"district\": \"district \",\r\n                        \"state\": \"state \",\r\n                        \"postalCode\": \" postalCode\",\r\n                        \"country\": {\r\n                            \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                            \"code\": \"C2365x\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Portugal\"\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        ],\r\n        \"studyPhase\": {\r\n            \"aliasCodeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f616\",\r\n            \"standardCode\": {\r\n                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                \"code\": \"C49686\",\r\n                \"codeSystem\": \"http:www.cdisc.org\",\r\n                \"codeSystemVersion\": \"2022-03-25\",\r\n                \"decode\": \"Phase IIa Trial\"\r\n            },\r\n            \"standardCodeAliases\": [\r\n                {\r\n                    \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                    \"code\": \"C49686\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"2022-03-25\",\r\n                    \"decode\": \"Phase IIa Trial\"\r\n                }\r\n            ]\r\n        },\r\n        \"businessTherapeuticAreas\": [\r\n            {\r\n                \"codeId\": \"6b190647-9ac4-4558-a88b-4b5496a2600c\",\r\n                \"code\": \"C158288\",\r\n                \"codeSystem\": \"http:www.cdisc.org\",\r\n                \"codeSystemVersion\": \"2022-03-25\",\r\n                \"decode\": \"Biosimilarity Study\"\r\n            }\r\n        ],\r\n        \"studyProtocolVersions\": [\r\n            {\r\n                \"studyProtocolVersionId\": \"72fa3742-1bad-42dc-8fdb-8d1c0379c75f\",\r\n                \"briefTitle\": \"Short\",\r\n                \"officialTitle\": \"Very Official\",\r\n                \"protocolAmendment\": \"Ammendment\",\r\n                \"protocolEffectiveDate\": \"2022-01-01\",\r\n                \"protocolStatus\": {\r\n                    \"codeId\": \"ab83b043-178f-4a8c-868a-e474f500888b\",\r\n                    \"code\": \"C1113x\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"1\",\r\n                    \"decode\": \"FINAL 1\"\r\n                },\r\n                \"protocolVersion\": \"1\",\r\n                \"publicTitle\": \"Public Voice\",\r\n                \"scientificTitle\": \"Incomprehensible\"\r\n            }\r\n        ],\r\n        \"studyDesigns\": [\r\n            {\r\n                \"studyDesignId\": \"SD01\",\r\n                \"studyDesignName\": \"Design for Stage III\",\r\n                \"studyDesignDescription\": \"Stage III Biliary Duct Cancer\",\r\n                \"trialIntentType\": [\r\n                    {\r\n                        \"codeId\": \"9fc53378-ac85-463d-8f9f-587bba5bb605\",\r\n                        \"code\": \"C15714\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Basic Research\"\r\n                    }\r\n                ],\r\n                \"trialType\": [\r\n                    {\r\n                        \"codeId\": \"dc752a27-4afa-48c5-9dc1-1717e6a099ab\",\r\n                        \"code\": \"C158288\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Biosimilarity Study\"\r\n                    }\r\n                ],\r\n                \"interventionModel\": {\r\n                    \"codeId\": \"4997ed7c-ce9a-4459-bf5d-19d9a8d5c772\",\r\n                    \"code\": \"C82639\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"2022-03-25\",\r\n                    \"decode\": \"Parallel\"\r\n                },\r\n                \"studyCells\": [\r\n                    {\r\n                        \"studyCellId\": \"4614b061-f72c-43e1-aeb2-db1e4d312711\",\r\n                        \"studyArm\": {\r\n                            \"studyArmId\": \"different\",\r\n                            \"studyArmDataOriginDescription\": \"Captured subject data\",\r\n                            \"studyArmDataOriginType\": {\r\n                                \"codeId\": \"281a3135-5ab6-47c4-a136-92fb88d57f0d\",\r\n                                \"code\": \"C6574y\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"SUBJECT DATA\"\r\n                            },\r\n                            \"studyArmDescription\": \"The Placebo Arm\",\r\n                            \"studyArmName\": \"Placebo\",\r\n                            \"studyArmType\": {\r\n                                \"codeId\": \"77c714cf-ac76-4843-a4b6-1c68b8be7590\",\r\n                                \"code\": \"C174268\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Placebo Control Arm\"\r\n                            }\r\n                        },\r\n                        \"studyEpoch\": {\r\n                            \"studyEpochId\": \"32616f8e-aee7-4038-9d6f-ed2adb8879d9\",\r\n                            \"nextStudyEpochId\": \"\",\r\n                            \"previousStudyEpochId\": \"\",\r\n                            \"studyEpochDescription\": \"The run in\",\r\n                            \"studyEpochName\": \"Run In\",\r\n                            \"studyEpochType\": {\r\n                                \"codeId\": \"91603043-8496-4a71-aabc-2d6848bbc87d\",\r\n                                \"code\": \"C98779\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Run-in Period\"\r\n                            },\r\n                            \"encounters\": [\r\n                                \"VIS11\"\r\n                            ]\r\n                        },\r\n                        \"studyElements\": [\r\n                            {\r\n                                \"studyElementId\": \"00ad20fa-bb06-4b93-b237-6bc37a3810e5\",\r\n                                \"studyElementDescription\": \"First element\",\r\n                                \"studyElementName\": \"Element 1\",\r\n                                \"transitionStartRule\": {\r\n                                    \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                                    \"transitionRuleDescription\": \"Start Rule\"\r\n                                },\r\n                                \"transitionEndRule\": {\r\n                                    \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                                    \"transitionRuleDescription\": \"End Rule\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"studyCellId\": \"4614b061-f2-db1e4d312711\",\r\n                        \"studyArm\": {\r\n                            \"studyArmId\": \"different1\",\r\n                            \"studyArmDataOriginDescription\": \"Captured subject data\",\r\n                            \"studyArmDataOriginType\": {\r\n                                \"codeId\": \"281a3135-5ab6-47c4-a136-92fb88d57f0d\",\r\n                                \"code\": \"C6574y\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"SUBJECT DATA\"\r\n                            },\r\n                            \"studyArmDescription\": \"The Placebo Arm\",\r\n                            \"studyArmName\": \"Placebo\",\r\n                            \"studyArmType\": {\r\n                                \"codeId\": \"77c714cf-ac76-4843-a4b6-1c68b8be7590\",\r\n                                \"code\": \"C174268\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Placebo Control Arm\"\r\n                            }\r\n                        },\r\n                        \"studyEpoch\": {\r\n                            \"studyEpochId\": \"32616f8e-aee7-4038-9d6f-ed2adb8879d9\",\r\n                            \"nextStudyEpochId\": \"\",\r\n                            \"previousStudyEpochId\": \"\",\r\n                            \"studyEpochDescription\": \"The run in\",\r\n                            \"studyEpochName\": \"Run In\",\r\n                            \"studyEpochType\": {\r\n                                \"codeId\": \"91603043-8496-4a71-aabc-2d6848bbc87d\",\r\n                                \"code\": \"C98779\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Run-in Period\"\r\n                            },\r\n                            \"encounters\": [\r\n                                \"VIS11\"\r\n                            ]\r\n                        },\r\n                        \"studyElements\": [\r\n                            {\r\n                                \"studyElementId\": \"00ad20fa-bb06-4b93-b237-6bc37a3810e5\",\r\n                                \"studyElementDescription\": \"First element\",\r\n                                \"studyElementName\": \"Element 1\",\r\n                                \"transitionStartRule\": {\r\n                                    \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                                    \"transitionRuleDescription\": \"Start Rule\"\r\n                                },\r\n                                \"transitionEndRule\": {\r\n                                    \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                                    \"transitionRuleDescription\": \"End Rule\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyIndications\": [\r\n                    {\r\n                        \"indicationId\": \"c14a9bd2-938b-490d-ac47-4b4483e304b9\",\r\n                        \"indicationDescription\": \"Something bad\",\r\n                        \"codes\": [\r\n                            {\r\n                                \"codeId\": \"a387aa89-c025-438e-91dd-b08281d122d0\",\r\n                                \"code\": \"C6666x\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"BAD STUFF\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyInvestigationalInterventions\": [\r\n                    {\r\n                        \"investigationalInterventionId\": \"IN001\",\r\n                        \"interventionDescription\": \"Intervention 1\",\r\n                        \"codes\": [\r\n                            {\r\n                                \"codeId\": \"00ce1a32-2526-4016-9624-28139a929090\",\r\n                                \"code\": \"C7639x\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"MODEL 1\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyPopulations\": [\r\n                    {\r\n                        \"studyDesignPopulationId\": \"ef300f84-2sa479w-415b-adf1-dad41df6169cd5\",\r\n                        \"populationDescription\": \"Population 1\",\r\n                        \"plannedNumberOfParticipants\": 2,\r\n                        \"plannedMaximumAgeOfParticipants\": \"88\",\r\n                        \"plannedMinimumAgeOfParticipants\": \"23\",\r\n                        \"plannedSexOfParticipants\": [\r\n                            {\r\n                                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f614\",\r\n                                \"code\": \"C49636\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Both\"\r\n                            },\r\n                            {\r\n                                \"codeId\": \"61ad4d8f--4bbf6141f614\",\r\n                                \"code\": \"C16576\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Male\"\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"studyDesignPopulationId\": \"ef300f84-2saf1-dad41df6169cd5\",\r\n                        \"populationDescription\": \"Population 1\",\r\n                        \"plannedNumberOfParticipants\": 2,\r\n                        \"plannedMaximumAgeOfParticipants\": \"80\",\r\n                        \"plannedMinimumAgeOfParticipants\": \"21\",\r\n                        \"plannedSexOfParticipants\": [\r\n                            {\r\n                                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f614\",\r\n                                \"code\": \"C49636\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Both\"\r\n                            },\r\n                            {\r\n                                \"codeId\": \"61ad4d8f--4bbf6141f614\",\r\n                                \"code\": \"C16576\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Male\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyObjectives\": [\r\n                    {\r\n                        \"objectiveId\": \"1cedd7c7-65b0-4c27e9d\",\r\n                        \"objectiveDescription\": \"Objective Level 1\",\r\n                        \"objectiveLevel\": {\r\n                            \"codeId\": \"2ae454b4-b37b-4986-9b3c-d5044bc4aec7\",\r\n                            \"code\": \"C85826\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Study Primary Objective\"\r\n                        },\r\n                        \"objectiveEndpoints\": [\r\n                            {\r\n                                \"endpointId\": \"END001\",\r\n                                \"endpointDescription\": \"Endpoint 1\",\r\n                                \"endpointPurposeDescription\": \"level description\",\r\n                                \"endpointLevel\": {\r\n                                    \"codeId\": \"END001\",\r\n                                    \"code\": \"C9834x\",\r\n                                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                                    \"codeSystemVersion\": \"1\",\r\n                                    \"decode\": \"PURPOSE\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"objectiveId\": \"1cedd7c7-65b0-48b4-ac93-cbe046c27e9d\",\r\n                        \"objectiveDescription\": \"Objective Level 1\",\r\n                        \"objectiveLevel\": {\r\n                            \"codeId\": \"2ae454b4-b37b-4986-9b3c-d5044bc4aec7\",\r\n                            \"code\": \"C85827\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Study Secondary Objective\"\r\n                        },\r\n                        \"objectiveEndpoints\": [\r\n                            {\r\n                                \"endpointId\": \"END001\",\r\n                                \"endpointDescription\": \"Endpoint 1\",\r\n                                \"endpointPurposeDescription\": \"level description\",\r\n                                \"endpointLevel\": {\r\n                                    \"codeId\": \"END001\",\r\n                                    \"code\": \"C9834x\",\r\n                                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                                    \"codeSystemVersion\": \"1\",\r\n                                    \"decode\": \"PURPOSE\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyScheduleTimelines\": [\r\n                    {\r\n                        \"scheduleTimelineId\": \"Timeline01\",\r\n                        \"scheduleTimelineName\": \"name\",\r\n                        \"scheduleTimelineDescription\": \"Timeline for Stage III\",\r\n                        \"entryCondition\": \"condition\",\r\n                        \"scheduleTimelineEntryId\": \"INS001\",\r\n                        \"scheduleTimelineExits\": [\r\n                            {\r\n                                \"scheduleTimelineExitId\": \"Exit01\"\r\n                            }\r\n                        ],\r\n                        \"scheduleTimelineInstances\": [\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT001\",\r\n                                    \"ACT002\",\r\n                                    \"ACT003\",\r\n                                    \"ACT005\",\r\n                                    \"ACT008\",\r\n                                    \"ACT009\",\r\n                                    \"ACT011\",\r\n                                    \"ACT018\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS002\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS11\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 1,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T01\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T02\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"DECISION\",\r\n                                \"conditionAssignments\": {\r\n                                    \"option\": \"changes\"\r\n                                },\r\n                                \"scheduledInstanceId\": \"INS001\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS11\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 2,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T030\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T04\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT003\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS003\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS12\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T03\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T04\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT005\",\r\n                                    \"ACT006\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS004\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS13\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T06\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 10\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT006\",\r\n                                    \"ACT007\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS005\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS14\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T07\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 18\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT008\",\r\n                                    \"ACT009\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS006\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS15\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T08\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 20\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT010\",\r\n                                    \"ACT011\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS007\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS16\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T09\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 23\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT012\",\r\n                                    \"ACT013\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS008\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS17\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T10\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 25\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT013\",\r\n                                    \"ACT014\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS009\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS18\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T11\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 30\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT015\",\r\n                                    \"ACT016\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS010\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS19\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T12\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 35\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT017\",\r\n                                    \"ACT018\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS011\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS20\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T13\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT018\",\r\n                                    \"ACT019\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS012\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS21\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T14\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 3\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT019\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS013\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS22\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T15\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 5\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT003\",\r\n                                    \"ACT001\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS014\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS23\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T16\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 6\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT005\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS015\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS23\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T17\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"MONTH 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"therapeuticAreas\": [\r\n                    {\r\n                        \"codeId\": \"6b190647-9ac4-4558-a88b-4b5496a2600c\",\r\n                        \"code\": \"C158288\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Biosimilarity Study\"\r\n                    }\r\n                ],\r\n                \"studyEstimands\": [\r\n                    {\r\n                        \"estimandId\": \"0bc02031-b058-43f8-9888-0f66f59b825f\",\r\n                        \"treatment\": \"IN001\",\r\n                        \"summaryMeasure\": \"TEST\",\r\n                        \"analysisPopulation\": {\r\n                            \"analysisPopulationId\": \"77af2751-1707-4edc-a5db-350ebe7233d8\",\r\n                            \"populationDescription\": \"Population 1\"\r\n                        },\r\n                        \"variableOfInterest\": \"END001\",\r\n                        \"intercurrentEvents\": [\r\n                            {\r\n                                \"intercurrentEventId\": \"1dc5c1e9-7b21-4455-9f37-ff2fadb8ac65\",\r\n                                \"intercurrentEventDescription\": \"Event Description\",\r\n                                \"intercurrentEventName\": \"Event Name\",\r\n                                \"intercurrentEventStrategy\": \"Strategies\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"encounters\": [\r\n                    {\r\n                        \"encounterId\": \"VIS11\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                            \"code\": \"C25299\",\r\n                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Diastolic Blood Pressure\"\r\n                        },\r\n                        \"encounterName\": \"SCREENING VISIT\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                            \"code\": \"C25299\",\r\n                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Diastolic Blood Pressure\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS12\",\r\n                        \"previousEncounterId\": \"\",\r\n                        \"encounterScheduledAtTimingId\": \"T030\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS12\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS13\",\r\n                        \"previousEncounterId\": \"VIS11\",\r\n                        \"encounterScheduledAtTimingId\": \"T02\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS13\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS14\",\r\n                        \"previousEncounterId\": \"VIS12\",\r\n                        \"encounterScheduledAtTimingId\": \"T03\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS14\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 3\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS15\",\r\n                        \"previousEncounterId\": \"VIS13\",\r\n                        \"encounterScheduledAtTimingId\": \"T04\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS15\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS16\",\r\n                        \"previousEncounterId\": \"VIS14\",\r\n                        \"encounterScheduledAtTimingId\": \"T03\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS16\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS17\",\r\n                        \"previousEncounterId\": \"VIS15\",\r\n                        \"encounterScheduledAtTimingId\": \"T06\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS17\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 3\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS18\",\r\n                        \"previousEncounterId\": \"VIS16\",\r\n                        \"encounterScheduledAtTimingId\": \"T07\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS18\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 2, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS19\",\r\n                        \"previousEncounterId\": \"VIS17\",\r\n                        \"encounterScheduledAtTimingId\": \"T08\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS19\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 2, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS20\",\r\n                        \"previousEncounterId\": \"VIS18\",\r\n                        \"encounterScheduledAtTimingId\": \"T09\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS20\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 3, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS21\",\r\n                        \"previousEncounterId\": \"VIS19\",\r\n                        \"encounterScheduledAtTimingId\": \"T10\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS21\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 3, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS22\",\r\n                        \"previousEncounterId\": \"VIS20\",\r\n                        \"encounterScheduledAtTimingId\": \"T11\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS22\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"FU 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS23\",\r\n                        \"previousEncounterId\": \"VIS21\",\r\n                        \"encounterScheduledAtTimingId\": \"T16\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS23\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"VIRTUAL VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"FU 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"\",\r\n                        \"previousEncounterId\": \"VIS22\",\r\n                        \"encounterScheduledAtTimingId\": \"T17\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    }\r\n                ],\r\n                \"activities\": [\r\n                    {\r\n                        \"activityId\": \"ACT001\",\r\n                        \"activityDescription\": \"Informed consents\",\r\n                        \"activityName\": \"Informed consent\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT002\",\r\n                        \"previousActivityId\": \"\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"Reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT002\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Eligibility criteria\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT003\",\r\n                        \"previousActivityId\": \"ACT001\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT003\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Demography\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT004\",\r\n                        \"previousActivityId\": \"ACT002\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT004\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Medical history\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT005\",\r\n                        \"previousActivityId\": \"ACT003\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT005\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Disease characteristics\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT006\",\r\n                        \"previousActivityId\": \"ACT004\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT006\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Physical exam\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT007\",\r\n                        \"previousActivityId\": \"ACT005\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT007\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Height\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT008\",\r\n                        \"previousActivityId\": \"ACT006\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT008\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"12-lead ECG\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT009\",\r\n                        \"previousActivityId\": \"ACT007\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT009\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Hematology (predose)\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT010\",\r\n                        \"previousActivityId\": \"ACT008\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT010\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Chemistry (predose)\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT011\",\r\n                        \"previousActivityId\": \"ACT009\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT011\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Serology\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT012\",\r\n                        \"previousActivityId\": \"ACT010\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT012\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Urinalysis\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT013\",\r\n                        \"previousActivityId\": \"ACT011\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT013\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Pregnancy test\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT014\",\r\n                        \"previousActivityId\": \"ACT012\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT014\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Ensure availability of medication X\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT015\",\r\n                        \"previousActivityId\": \"ACT013\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT015\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Hospitalization\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT016\",\r\n                        \"previousActivityId\": \"ACT014\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT016\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Weight\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT017\",\r\n                        \"previousActivityId\": \"ACT015\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT017\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Vital signs\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT018\",\r\n                        \"previousActivityId\": \"ACT016\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT018\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"adverse events\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT019\",\r\n                        \"previousActivityId\": \"ACT017\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT019\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Concomitant medications\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"\",\r\n                        \"previousActivityId\": \"ACT018\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    }\r\n                ],\r\n                \"studyDesignRationale\": \"Rationale \",\r\n                \"studyDesignBlindingScheme\": {\r\n                    \"aliasCodeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f616\",\r\n                    \"standardCode\": {\r\n                        \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                        \"code\": \"C49686\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Phase IIa Trial\"\r\n                    },\r\n                    \"standardCodeAliases\": [\r\n                        {\r\n                            \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                            \"code\": \"C49686\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Phase IIa Trial\"\r\n                        }\r\n                    ]\r\n                },\r\n                \"biomedicalConcepts\": [\r\n                    {\r\n                        \"biomedicalConceptId\": \"BC11\",\r\n                        \"bcName\": \"Diastolic Blood Pressure \",\r\n                        \"bcSynonyms\": [\r\n                            \"DIABP\",\r\n                            \"DIA BP\",\r\n                            \"Blood pressure diastolic\",\r\n                            \"BP dias\",\r\n                            \"BP diastolic\",\r\n                            \"DBP\",\r\n                            \"Dias\",\r\n                            \"Dias BP\",\r\n                            \"Diast\",\r\n                            \"Diastoli \"\r\n                        ],\r\n                        \"bcReference\": \"href\",\r\n                        \"bcProperties\": [\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropId1\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": true,\r\n                                \"bcPropertyEnabled\": false,\r\n                                \"bcPropertyDataType\": \"strings\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"mmHg\"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"C25299\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Diastolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8462-4\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Diastolic Blood Pressure\"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            },\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropIds\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": false,\r\n                                \"bcPropertyEnabled\": true,\r\n                                \"bcPropertyDataType\": \"string\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"mmHg\"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"C25299\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Diastolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8462-4\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Diastolic Blood Pressure\"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            }\r\n                        ],\r\n                        \"bcConceptCode\": {\r\n                            \"aliasCodeId\": \"aliasId\",\r\n                            \"standardCode\": {\r\n                                \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                \"code\": \"C25299\",\r\n                                \"codeSystem\": \"http://www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-27\",\r\n                                \"decode\": \"Diastolic Blood Pressure\"\r\n                            },\r\n                            \"standardCodeAliases\": [\r\n                                {\r\n                                    \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                    \"code\": \"8462-4\",\r\n                                    \"codeSystem\": \"http://loinc.org/\",\r\n                                    \"codeSystemVersion\": \"2022-03-25\",\r\n                                    \"decode\": \"Diastolic Blood Pressure\"\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptId\": \"BC2\",\r\n                        \"bcName\": \"Systolic Blood Pressure\",\r\n                        \"bcSynonyms\": [\r\n                            \"SYSBP\",\r\n                            \"SYS BP\",\r\n                            \"Blood pressure systolic \"\r\n                        ],\r\n                        \"bcReference\": \"https://www.google.com\",\r\n                        \"bcProperties\": [\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropId\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": true,\r\n                                \"bcPropertyEnabled\": false,\r\n                                \"bcPropertyDataType\": \"string\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-24\",\r\n                                            \"decode\": \"mmHg \"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"Cxxxx\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Systolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8xx-x\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Systolic Blood Pressure \"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            }\r\n                        ],\r\n                        \"bcConceptCode\": {\r\n                            \"aliasCodeId\": \"aliasId\",\r\n                            \"standardCode\": {\r\n                                \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                \"code\": \"C25299\",\r\n                                \"codeSystem\": \"http://www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Diastolic Blood Pressure\"\r\n                            },\r\n                            \"standardCodeAliases\": [\r\n                                {\r\n                                    \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                    \"code\": \"8462-4\",\r\n                                    \"codeSystem\": \"http://loinc.org/\",\r\n                                    \"codeSystemVersion\": \"2022-03-2\",\r\n                                    \"decode\": \"Diastolic Blood Pressure\"\r\n                                }\r\n                            ]\r\n                        }\r\n                    }\r\n                ],\r\n                \"bcCategories\": [\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC1\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC4\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC2\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Blood pressure\",\r\n                        \"bcCategoryDescription\": \"Blood pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC2\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC3\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Blood Pressure Tests\",\r\n                        \"bcCategoryDescription\": \"Blood Pressure Test\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC2\",\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC3\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC2\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [],\r\n                        \"bcCategoryName\": \"Diastolic Blood Pressure\",\r\n                        \"bcCategoryDescription\": \"Diastolic Blood Pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC4\",\r\n                        \"bcCategoryParentIds\": [],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Systolic Blood Pressure\",\r\n                        \"bcCategoryDescription\": \"Systolic Blood Pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC2\"\r\n                        ]\r\n                    }\r\n                ],\r\n                \"bcSurrogates\": [\r\n                    {\r\n                        \"bcSurrogateId\": \"BCS1\",\r\n                        \"bcSurrogateName\": \"Diastolic Blood Pressure\",\r\n                        \"bcSurrogateDescription\": \"Diastolic Blood Pressure\",\r\n                        \"bcSurrogateReference\": \"www.google.com\"\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    },\r\n    \"auditTrail\": {\r\n        \"entryDateTime\": \"2023-03-23T10:10:52.3450729Z\",\r\n        \"usdmVersion\": \"1.9\",\r\n        \"SDRUploadVersion\": 1\r\n    },\r\n    \"links\": {\r\n        \"studyDefinitions\": \"/v2/studydefinitions/10473d92-3775-4ebe-b309-af27b67f691e?sdruploadversion=1\",\r\n        \"auditTrail\": \"/studydefinitions/10473d92-3775-4ebe-b309-af27b67f691e/audittrail\",\r\n        \"studyDesigns\": [\r\n            {\r\n                \"studyDesignId\": \"SD01\",\r\n                \"studyDesignLink\": \"/v2/studydesigns?study_uuid=10473d92-3775-4ebe-b309-af27b67f691e&sdruploadversion=1&studydesign_uuid=SD01\"\r\n            }\r\n        ],\r\n        \"SoA\": \"/v2/studydefinitions/10473d92-3775-4ebe-b309-af27b67f691e/studydesigns/soa?sdruploadversion=1\"\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/v2/studydefinitions", "host": ["{{URL}}"], "path": ["v2", "studydefinitions"]}}, "response": []}, {"name": "PUT Study", "request": {"method": "PUT", "header": [{"key": "usdmVersion", "value": "1.9", "type": "default"}], "body": {"mode": "raw", "raw": "{\r\n    \"clinicalStudy\": {        \r\n        \"studyTitle\": \"Study of Stage III Biliary Duct Cancer\",\r\n        \"studyVersion\": \"1\",\r\n        \"studyType\": {\r\n            \"codeId\": \"7bbb6fd9-7e43-4dbf-90fa-3de117dc76d2\",\r\n            \"code\": \"C98388\",\r\n            \"codeSystem\": \"http:www.cdisc.org\",\r\n            \"codeSystemVersion\": \"2022-03-24\",\r\n            \"decode\": \"INTERVENTIONAL\"\r\n        },\r\n        \"studyRationale\": \"Rational\",\r\n        \"studyAcronym\": \"Acronym\",\r\n        \"studyIdentifiers\": [\r\n            {\r\n                \"studyIdentifierId\": \"c4663375-29be-4b37-9a7d-b2ef927fdf64\",\r\n                \"studyIdentifier\": \"CT-GOV-1234\",\r\n                \"studyIdentifierScope\": {\r\n                    \"organisationId\": \"96dd6b83-2d34-45ac-9d08-3e647b2c7316\",\r\n                    \"organisationIdentifier\": \"CT-GOV\",\r\n                    \"organisationIdentifierScheme\": \"FDA\",\r\n                    \"organisationName\": \"ClinicalTrials.gov\",\r\n                    \"organisationType\": {\r\n                        \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                        \"code\": \"C2365x\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"1\",\r\n                        \"decode\": \"Clinical Study Sponsor\"\r\n                    },\r\n                    \"organizationLegalAddress\": {\r\n                        \"text\": \"text\",\r\n                        \"line\": \" line2\",\r\n                        \"city\": \" city\",\r\n                        \"district\": \"district \",\r\n                        \"state\": \"state \",\r\n                        \"postalCode\": \" postalCode\",\r\n                        \"country\": {\r\n                            \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                            \"code\": \"C2365x\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Egypt\"\r\n                        }\r\n                    }\r\n                }\r\n            },\r\n            {\r\n                \"studyIdentifierId\": \"c3f0bac9-72e4-4df6-98ed-e364d10c8239\",\r\n                \"studyIdentifier\": \"CT-GOV\",\r\n                \"studyIdentifierScope\": {\r\n                    \"organisationId\": \"502db4f4-3573-444c-b0a1-ac677e46556a\",\r\n                    \"organisationIdentifier\": \"CT-GOV\",\r\n                    \"organisationIdentifierScheme\": \"FDA\",\r\n                    \"organisationName\": \"ClinicalTrials.gov\",\r\n                    \"organisationType\": {\r\n                        \"codeId\": \"d036ab0b-ce9c-4857-838f-07888180d161\",\r\n                        \"code\": \"C2365x\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"1\",\r\n                        \"decode\": \"Regulatory Agency\"\r\n                    },\r\n                    \"organizationLegalAddress\": {\r\n                        \"text\": \"text\",\r\n                        \"line\": \" line2\",\r\n                        \"city\": \" city\",\r\n                        \"district\": \"district \",\r\n                        \"state\": \"state \",\r\n                        \"postalCode\": \" postalCode\",\r\n                        \"country\": {\r\n                            \"codeId\": \"8fcecddf-bbe2-4e71-ad97-114cbc3f1cba\",\r\n                            \"code\": \"C2365x\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Portugal\"\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        ],\r\n        \"studyPhase\": {\r\n            \"aliasCodeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f616\",\r\n            \"standardCode\": {\r\n                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                \"code\": \"C49686\",\r\n                \"codeSystem\": \"http:www.cdisc.org\",\r\n                \"codeSystemVersion\": \"2022-03-25\",\r\n                \"decode\": \"Phase IIa Trial\"\r\n            },\r\n            \"standardCodeAliases\": [\r\n                {\r\n                    \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                    \"code\": \"C49686\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"2022-03-25\",\r\n                    \"decode\": \"Phase IIa Trial\"\r\n                }\r\n            ]\r\n        },\r\n        \"businessTherapeuticAreas\": [\r\n            {\r\n                \"codeId\": \"6b190647-9ac4-4558-a88b-4b5496a2600c\",\r\n                \"code\": \"C158288\",\r\n                \"codeSystem\": \"http:www.cdisc.org\",\r\n                \"codeSystemVersion\": \"2022-03-25\",\r\n                \"decode\": \"Biosimilarity Study\"\r\n            }\r\n        ],\r\n        \"studyProtocolVersions\": [\r\n            {\r\n                \"studyProtocolVersionId\": \"72fa3742-1bad-42dc-8fdb-8d1c0379c75f\",\r\n                \"briefTitle\": \"Short\",\r\n                \"officialTitle\": \"Very Official\",\r\n                \"protocolAmendment\": \"Ammendment\",\r\n                \"protocolEffectiveDate\": \"2022-01-01\",\r\n                \"protocolStatus\": {\r\n                    \"codeId\": \"ab83b043-178f-4a8c-868a-e474f500888b\",\r\n                    \"code\": \"C1113x\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"1\",\r\n                    \"decode\": \"FINAL 1\"\r\n                },\r\n                \"protocolVersion\": \"1\",\r\n                \"publicTitle\": \"Public Voice\",\r\n                \"scientificTitle\": \"Incomprehensible\"\r\n            }\r\n        ],\r\n        \"studyDesigns\": [\r\n            {\r\n                \"studyDesignId\": \"SD01\",\r\n                \"studyDesignName\": \"Design for Stage III\",\r\n                \"studyDesignDescription\": \"Stage III Biliary Duct Cancer\",\r\n                \"trialIntentType\": [\r\n                    {\r\n                        \"codeId\": \"9fc53378-ac85-463d-8f9f-587bba5bb605\",\r\n                        \"code\": \"C15714\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Basic Research\"\r\n                    }\r\n                ],\r\n                \"trialType\": [\r\n                    {\r\n                        \"codeId\": \"dc752a27-4afa-48c5-9dc1-1717e6a099ab\",\r\n                        \"code\": \"C158288\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Biosimilarity Study\"\r\n                    }\r\n                ],\r\n                \"interventionModel\": {\r\n                    \"codeId\": \"4997ed7c-ce9a-4459-bf5d-19d9a8d5c772\",\r\n                    \"code\": \"C82639\",\r\n                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                    \"codeSystemVersion\": \"2022-03-25\",\r\n                    \"decode\": \"Parallel\"\r\n                },\r\n                \"studyCells\": [\r\n                    {\r\n                        \"studyCellId\": \"4614b061-f72c-43e1-aeb2-db1e4d312711\",\r\n                        \"studyArm\": {\r\n                            \"studyArmId\": \"different\",\r\n                            \"studyArmDataOriginDescription\": \"Captured subject data\",\r\n                            \"studyArmDataOriginType\": {\r\n                                \"codeId\": \"281a3135-5ab6-47c4-a136-92fb88d57f0d\",\r\n                                \"code\": \"C6574y\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"SUBJECT DATA\"\r\n                            },\r\n                            \"studyArmDescription\": \"The Placebo Arm\",\r\n                            \"studyArmName\": \"Placebo\",\r\n                            \"studyArmType\": {\r\n                                \"codeId\": \"77c714cf-ac76-4843-a4b6-1c68b8be7590\",\r\n                                \"code\": \"C174268\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Placebo Control Arm\"\r\n                            }\r\n                        },\r\n                        \"studyEpoch\": {\r\n                            \"studyEpochId\": \"32616f8e-aee7-4038-9d6f-ed2adb8879d9\",\r\n                            \"nextStudyEpochId\": \"\",\r\n                            \"previousStudyEpochId\": \"\",\r\n                            \"studyEpochDescription\": \"The run in\",\r\n                            \"studyEpochName\": \"Run In\",\r\n                            \"studyEpochType\": {\r\n                                \"codeId\": \"91603043-8496-4a71-aabc-2d6848bbc87d\",\r\n                                \"code\": \"C98779\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Run-in Period\"\r\n                            },\r\n                            \"encounters\": [\r\n                                \"VIS11\"\r\n                            ]\r\n                        },\r\n                        \"studyElements\": [\r\n                            {\r\n                                \"studyElementId\": \"00ad20fa-bb06-4b93-b237-6bc37a3810e5\",\r\n                                \"studyElementDescription\": \"First element\",\r\n                                \"studyElementName\": \"Element 1\",\r\n                                \"transitionStartRule\": {\r\n                                    \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                                    \"transitionRuleDescription\": \"Start Rule\"\r\n                                },\r\n                                \"transitionEndRule\": {\r\n                                    \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                                    \"transitionRuleDescription\": \"End Rule\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"studyCellId\": \"4614b061-f2-db1e4d312711\",\r\n                        \"studyArm\": {\r\n                            \"studyArmId\": \"different1\",\r\n                            \"studyArmDataOriginDescription\": \"Captured subject data\",\r\n                            \"studyArmDataOriginType\": {\r\n                                \"codeId\": \"281a3135-5ab6-47c4-a136-92fb88d57f0d\",\r\n                                \"code\": \"C6574y\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"SUBJECT DATA\"\r\n                            },\r\n                            \"studyArmDescription\": \"The Placebo Arm\",\r\n                            \"studyArmName\": \"Placebo\",\r\n                            \"studyArmType\": {\r\n                                \"codeId\": \"77c714cf-ac76-4843-a4b6-1c68b8be7590\",\r\n                                \"code\": \"C174268\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Placebo Control Arm\"\r\n                            }\r\n                        },\r\n                        \"studyEpoch\": {\r\n                            \"studyEpochId\": \"32616f8e-aee7-4038-9d6f-ed2adb8879d9\",\r\n                            \"nextStudyEpochId\": \"\",\r\n                            \"previousStudyEpochId\": \"\",\r\n                            \"studyEpochDescription\": \"The run in\",\r\n                            \"studyEpochName\": \"Run In\",\r\n                            \"studyEpochType\": {\r\n                                \"codeId\": \"91603043-8496-4a71-aabc-2d6848bbc87d\",\r\n                                \"code\": \"C98779\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Run-in Period\"\r\n                            },\r\n                            \"encounters\": [\r\n                                \"VIS11\"\r\n                            ]\r\n                        },\r\n                        \"studyElements\": [\r\n                            {\r\n                                \"studyElementId\": \"00ad20fa-bb06-4b93-b237-6bc37a3810e5\",\r\n                                \"studyElementDescription\": \"First element\",\r\n                                \"studyElementName\": \"Element 1\",\r\n                                \"transitionStartRule\": {\r\n                                    \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                                    \"transitionRuleDescription\": \"Start Rule\"\r\n                                },\r\n                                \"transitionEndRule\": {\r\n                                    \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                                    \"transitionRuleDescription\": \"End Rule\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyIndications\": [\r\n                    {\r\n                        \"indicationId\": \"c14a9bd2-938b-490d-ac47-4b4483e304b9\",\r\n                        \"indicationDescription\": \"Something bad\",\r\n                        \"codes\": [\r\n                            {\r\n                                \"codeId\": \"a387aa89-c025-438e-91dd-b08281d122d0\",\r\n                                \"code\": \"C6666x\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"BAD STUFF\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyInvestigationalInterventions\": [\r\n                    {\r\n                        \"investigationalInterventionId\": \"IN001\",\r\n                        \"interventionDescription\": \"Intervention 1\",\r\n                        \"codes\": [\r\n                            {\r\n                                \"codeId\": \"00ce1a32-2526-4016-9624-28139a929090\",\r\n                                \"code\": \"C7639x\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"1\",\r\n                                \"decode\": \"MODEL 1\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyPopulations\": [\r\n                    {\r\n                        \"studyDesignPopulationId\": \"ef300f84-2sa479w-415b-adf1-dad41df6169cd5\",\r\n                        \"populationDescription\": \"Population 1\",\r\n                        \"plannedNumberOfParticipants\": 2,\r\n                        \"plannedMaximumAgeOfParticipants\": \"88\",\r\n                        \"plannedMinimumAgeOfParticipants\": \"23\",\r\n                        \"plannedSexOfParticipants\": [\r\n                            {\r\n                                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f614\",\r\n                                \"code\": \"C49636\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Both\"\r\n                            },\r\n                            {\r\n                                \"codeId\": \"61ad4d8f--4bbf6141f614\",\r\n                                \"code\": \"C16576\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Male\"\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"studyDesignPopulationId\": \"ef300f84-2saf1-dad41df6169cd5\",\r\n                        \"populationDescription\": \"Population 1\",\r\n                        \"plannedNumberOfParticipants\": 2,\r\n                        \"plannedMaximumAgeOfParticipants\": \"80\",\r\n                        \"plannedMinimumAgeOfParticipants\": \"21\",\r\n                        \"plannedSexOfParticipants\": [\r\n                            {\r\n                                \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f614\",\r\n                                \"code\": \"C49636\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Both\"\r\n                            },\r\n                            {\r\n                                \"codeId\": \"61ad4d8f--4bbf6141f614\",\r\n                                \"code\": \"C16576\",\r\n                                \"codeSystem\": \"http:www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Male\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyObjectives\": [\r\n                    {\r\n                        \"objectiveId\": \"1cedd7c7-65b0-4c27e9d\",\r\n                        \"objectiveDescription\": \"Objective Level 1\",\r\n                        \"objectiveLevel\": {\r\n                            \"codeId\": \"2ae454b4-b37b-4986-9b3c-d5044bc4aec7\",\r\n                            \"code\": \"C85826\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Study Primary Objective\"\r\n                        },\r\n                        \"objectiveEndpoints\": [\r\n                            {\r\n                                \"endpointId\": \"END001\",\r\n                                \"endpointDescription\": \"Endpoint 1\",\r\n                                \"endpointPurposeDescription\": \"level description\",\r\n                                \"endpointLevel\": {\r\n                                    \"codeId\": \"END001\",\r\n                                    \"code\": \"C9834x\",\r\n                                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                                    \"codeSystemVersion\": \"1\",\r\n                                    \"decode\": \"PURPOSE\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"objectiveId\": \"1cedd7c7-65b0-48b4-ac93-cbe046c27e9d\",\r\n                        \"objectiveDescription\": \"Objective Level 1\",\r\n                        \"objectiveLevel\": {\r\n                            \"codeId\": \"2ae454b4-b37b-4986-9b3c-d5044bc4aec7\",\r\n                            \"code\": \"C85827\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"1\",\r\n                            \"decode\": \"Study Secondary Objective\"\r\n                        },\r\n                        \"objectiveEndpoints\": [\r\n                            {\r\n                                \"endpointId\": \"END001\",\r\n                                \"endpointDescription\": \"Endpoint 1\",\r\n                                \"endpointPurposeDescription\": \"level description\",\r\n                                \"endpointLevel\": {\r\n                                    \"codeId\": \"END001\",\r\n                                    \"code\": \"C9834x\",\r\n                                    \"codeSystem\": \"http:www.cdisc.org\",\r\n                                    \"codeSystemVersion\": \"1\",\r\n                                    \"decode\": \"PURPOSE\"\r\n                                }\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"studyScheduleTimelines\": [\r\n                    {\r\n                        \"scheduleTimelineId\": \"Timeline01\",\r\n                        \"scheduleTimelineName\": \"name\",\r\n                        \"scheduleTimelineDescription\": \"Timeline for Stage III\",\r\n                        \"entryCondition\": \"condition\",\r\n                        \"scheduleTimelineEntryId\": \"INS001\",\r\n                        \"scheduleTimelineExits\": [\r\n                            {\r\n                                \"scheduleTimelineExitId\": \"Exit01\"\r\n                            }\r\n                        ],\r\n                        \"scheduleTimelineInstances\": [\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT001\",\r\n                                    \"ACT002\",\r\n                                    \"ACT003\",\r\n                                    \"ACT005\",\r\n                                    \"ACT008\",\r\n                                    \"ACT009\",\r\n                                    \"ACT011\",\r\n                                    \"ACT018\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS002\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS11\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 1,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T01\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T02\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"DECISION\",\r\n                                \"conditionAssignments\": {\r\n                                    \"option\": \"changes\"\r\n                                },\r\n                                \"scheduledInstanceId\": \"INS001\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS11\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 2,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T030\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T04\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT003\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS003\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS12\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T03\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    },\r\n                                    {\r\n                                        \"timingId\": \"T04\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"Before\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 15\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS002\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS001\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"EndToEnd\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT005\",\r\n                                    \"ACT006\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS004\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS13\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T06\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 10\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT006\",\r\n                                    \"ACT007\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS005\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS14\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T07\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 18\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT008\",\r\n                                    \"ACT009\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS006\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS15\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T08\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 20\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT010\",\r\n                                    \"ACT011\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS007\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS16\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T09\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 23\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT012\",\r\n                                    \"ACT013\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS008\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS17\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T10\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 25\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT013\",\r\n                                    \"ACT014\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS009\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS18\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T11\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 30\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT015\",\r\n                                    \"ACT016\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS010\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS19\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T12\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"Day 35\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT017\",\r\n                                    \"ACT018\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS011\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS20\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T13\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT018\",\r\n                                    \"ACT019\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS012\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS21\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T14\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 3\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT019\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS013\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS22\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T15\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 5\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT003\",\r\n                                    \"ACT001\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS014\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS23\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T16\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"WEEK 6\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            },\r\n                            {\r\n                                \"scheduledInstanceType\": \"ACTIVITY\",\r\n                                \"activityIds\": [\r\n                                    \"ACT005\",\r\n                                    \"ACT002\"\r\n                                ],\r\n                                \"scheduledInstanceId\": \"INS015\",\r\n                                \"scheduleTimelineExitId\": \"Exit01\",\r\n                                \"scheduledInstanceEncounterId\": \"VIS23\",\r\n                                \"scheduledInstanceTimelineId\": \"Timeline01\",\r\n                                \"scheduleSequenceNumber\": 3,\r\n                                \"scheduledInstanceTimings\": [\r\n                                    {\r\n                                        \"timingId\": \"T17\",\r\n                                        \"timingType\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"After\"\r\n                                        },\r\n                                        \"timingValue\": \"MONTH 1\",\r\n                                        \"timingWindow\": \"window\",\r\n                                        \"relativeToScheduledInstanceId\": \"INS001\",\r\n                                        \"relativeFromScheduledInstanceId\": \"INS002\",\r\n                                        \"timingRelativeToFrom\": {\r\n                                            \"codeId\": \"END001\",\r\n                                            \"code\": \"C9834x\",\r\n                                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"1\",\r\n                                            \"decode\": \"StartToStart\"\r\n                                        }\r\n                                    }\r\n                                ]\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"therapeuticAreas\": [\r\n                    {\r\n                        \"codeId\": \"6b190647-9ac4-4558-a88b-4b5496a2600c\",\r\n                        \"code\": \"C158288\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Biosimilarity Study\"\r\n                    }\r\n                ],\r\n                \"studyEstimands\": [\r\n                    {\r\n                        \"estimandId\": \"0bc02031-b058-43f8-9888-0f66f59b825f\",\r\n                        \"treatment\": \"IN001\",\r\n                        \"summaryMeasure\": \"TEST\",\r\n                        \"analysisPopulation\": {\r\n                            \"analysisPopulationId\": \"77af2751-1707-4edc-a5db-350ebe7233d8\",\r\n                            \"populationDescription\": \"Population 1\"\r\n                        },\r\n                        \"variableOfInterest\": \"END001\",\r\n                        \"intercurrentEvents\": [\r\n                            {\r\n                                \"intercurrentEventId\": \"1dc5c1e9-7b21-4455-9f37-ff2fadb8ac65\",\r\n                                \"intercurrentEventDescription\": \"Event Description\",\r\n                                \"intercurrentEventName\": \"Event Name\",\r\n                                \"intercurrentEventStrategy\": \"Strategies\"\r\n                            }\r\n                        ]\r\n                    }\r\n                ],\r\n                \"encounters\": [\r\n                    {\r\n                        \"encounterId\": \"VIS11\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                            \"code\": \"C25299\",\r\n                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Diastolic Blood Pressure\"\r\n                        },\r\n                        \"encounterName\": \"SCREENING VISIT\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                            \"code\": \"C25299\",\r\n                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Diastolic Blood Pressure\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS12\",\r\n                        \"previousEncounterId\": \"\",\r\n                        \"encounterScheduledAtTimingId\": \"T030\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS12\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS13\",\r\n                        \"previousEncounterId\": \"VIS11\",\r\n                        \"encounterScheduledAtTimingId\": \"T02\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS13\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS14\",\r\n                        \"previousEncounterId\": \"VIS12\",\r\n                        \"encounterScheduledAtTimingId\": \"T03\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS14\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"RUN-IN VISIT 3\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS15\",\r\n                        \"previousEncounterId\": \"VIS13\",\r\n                        \"encounterScheduledAtTimingId\": \"T04\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS15\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS16\",\r\n                        \"previousEncounterId\": \"VIS14\",\r\n                        \"encounterScheduledAtTimingId\": \"T03\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS16\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS17\",\r\n                        \"previousEncounterId\": \"VIS15\",\r\n                        \"encounterScheduledAtTimingId\": \"T06\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS17\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 1, TREATMENT 3\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS18\",\r\n                        \"previousEncounterId\": \"VIS16\",\r\n                        \"encounterScheduledAtTimingId\": \"T07\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS18\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 2, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS19\",\r\n                        \"previousEncounterId\": \"VIS17\",\r\n                        \"encounterScheduledAtTimingId\": \"T08\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS19\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 2, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS20\",\r\n                        \"previousEncounterId\": \"VIS18\",\r\n                        \"encounterScheduledAtTimingId\": \"T09\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS20\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 3, TREATMENT 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS21\",\r\n                        \"previousEncounterId\": \"VIS19\",\r\n                        \"encounterScheduledAtTimingId\": \"T10\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS21\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"CYCLE 3, TREATMENT 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS22\",\r\n                        \"previousEncounterId\": \"VIS20\",\r\n                        \"encounterScheduledAtTimingId\": \"T11\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS22\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"PLANNED VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"FU 1\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"VIS23\",\r\n                        \"previousEncounterId\": \"VIS21\",\r\n                        \"encounterScheduledAtTimingId\": \"T16\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    },\r\n                    {\r\n                        \"encounterId\": \"VIS23\",\r\n                        \"encounterContactModes\": [\r\n                            {\r\n                                \"codeId\": \"40c6a10a-da80-41746\",\r\n                                \"code\": \"767002\",\r\n                                \"codeSystem\": \"SNOMED-CT\",\r\n                                \"codeSystemVersion\": \"2022-05-31\",\r\n                                \"decode\": \"White blood cell counts\"\r\n                            }\r\n                        ],\r\n                        \"encounterDescription\": \"VIRTUAL VISIT\",\r\n                        \"encounterEnvironmentalSetting\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"encounterName\": \"FU 2\",\r\n                        \"encounterType\": {\r\n                            \"codeId\": \"40c6a10a-da80-41746\",\r\n                            \"code\": \"767002\",\r\n                            \"codeSystem\": \"SNOMED-CT\",\r\n                            \"codeSystemVersion\": \"2022-05-31\",\r\n                            \"decode\": \"White blood cell counts\"\r\n                        },\r\n                        \"nextEncounterId\": \"\",\r\n                        \"previousEncounterId\": \"VIS22\",\r\n                        \"encounterScheduledAtTimingId\": \"T17\",\r\n                        \"transitionStartRule\": {\r\n                            \"transitionRuleId\": \"14a6b9fa-f080-415b-83bd-6d6ffe5e4b60\",\r\n                            \"transitionRuleDescription\": \"Start Rule\"\r\n                        },\r\n                        \"transitionEndRule\": {\r\n                            \"transitionRuleId\": \"ae648ac6-f2c2-49d8-8ee2-64ccadec513e\",\r\n                            \"transitionRuleDescription\": \"End Rule\"\r\n                        }\r\n                    }\r\n                ],\r\n                \"activities\": [\r\n                    {\r\n                        \"activityId\": \"ACT001\",\r\n                        \"activityDescription\": \"Informed consents\",\r\n                        \"activityName\": \"Informed consent\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT002\",\r\n                        \"previousActivityId\": \"\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"Reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT002\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Eligibility criteria\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT003\",\r\n                        \"previousActivityId\": \"ACT001\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT003\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Demography\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT004\",\r\n                        \"previousActivityId\": \"ACT002\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT004\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Medical history\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT005\",\r\n                        \"previousActivityId\": \"ACT003\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT005\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Disease characteristics\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT006\",\r\n                        \"previousActivityId\": \"ACT004\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT006\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Physical exam\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT007\",\r\n                        \"previousActivityId\": \"ACT005\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT007\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Height\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT008\",\r\n                        \"previousActivityId\": \"ACT006\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT008\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"12-lead ECG\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT009\",\r\n                        \"previousActivityId\": \"ACT007\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT009\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Hematology (predose)\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT010\",\r\n                        \"previousActivityId\": \"ACT008\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT010\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Chemistry (predose)\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT011\",\r\n                        \"previousActivityId\": \"ACT009\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT011\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Serology\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT012\",\r\n                        \"previousActivityId\": \"ACT010\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT012\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Urinalysis\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT013\",\r\n                        \"previousActivityId\": \"ACT011\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT013\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Pregnancy test\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT014\",\r\n                        \"previousActivityId\": \"ACT012\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT014\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Ensure availability of medication X\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT015\",\r\n                        \"previousActivityId\": \"ACT013\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT015\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Hospitalization\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT016\",\r\n                        \"previousActivityId\": \"ACT014\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT016\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Weight\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT017\",\r\n                        \"previousActivityId\": \"ACT015\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT017\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Vital signs\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT018\",\r\n                        \"previousActivityId\": \"ACT016\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT018\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"adverse events\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"ACT019\",\r\n                        \"previousActivityId\": \"ACT017\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    },\r\n                    {\r\n                        \"activityId\": \"ACT019\",\r\n                        \"activityDescription\": \"Informed consent\",\r\n                        \"activityName\": \"Concomitant medications\",\r\n                        \"definedProcedures\": [\r\n                            {\r\n                                \"procedureId\": \"f4e2afcd-0b32-4d16-9247-bcc45604bc13\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-41746\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell counts\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection\",\r\n                                \"procedureIsConditional\": false,\r\n                                \"procedureIsConditionalReason\": \"Reasons\"\r\n                            },\r\n                            {\r\n                                \"procedureId\": \"f4e2\",\r\n                                \"procedureCode\": {\r\n                                    \"codeId\": \"40c6a10a-da80-4174-928c-88124a5f8e76\",\r\n                                    \"code\": \"767002\",\r\n                                    \"codeSystem\": \"SNOMED-CT\",\r\n                                    \"codeSystemVersion\": \"2022-05-31\",\r\n                                    \"decode\": \"White blood cell count\"\r\n                                },\r\n                                \"procedureType\": \"Specimen Collection1\",\r\n                                \"procedureIsConditional\": true,\r\n                                \"procedureIsConditionalReason\": \"Reason\"\r\n                            }\r\n                        ],\r\n                        \"nextActivityId\": \"\",\r\n                        \"previousActivityId\": \"ACT018\",\r\n                        \"activityIsConditional\": false,\r\n                        \"activityIsConditionalReason\": \"reason\",\r\n                        \"bcCategoryIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcSurrogateIds\": [\r\n                            \"BCS1\"\r\n                        ],\r\n                        \"biomedicalConceptIds\": [\r\n                            \"BC11\"\r\n                        ],\r\n                        \"activityTimelineId\": \"Timeline01\"\r\n                    }\r\n                ],\r\n                \"studyDesignRationale\": \"Rationale \",\r\n                \"studyDesignBlindingScheme\": {\r\n                    \"aliasCodeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f616\",\r\n                    \"standardCode\": {\r\n                        \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                        \"code\": \"C49686\",\r\n                        \"codeSystem\": \"http:www.cdisc.org\",\r\n                        \"codeSystemVersion\": \"2022-03-25\",\r\n                        \"decode\": \"Phase IIa Trial\"\r\n                    },\r\n                    \"standardCodeAliases\": [\r\n                        {\r\n                            \"codeId\": \"61ad4d8f-527c-4f1d-b227-4bbf6141f613\",\r\n                            \"code\": \"C49686\",\r\n                            \"codeSystem\": \"http:www.cdisc.org\",\r\n                            \"codeSystemVersion\": \"2022-03-25\",\r\n                            \"decode\": \"Phase IIa Trial\"\r\n                        }\r\n                    ]\r\n                },\r\n                \"biomedicalConcepts\": [\r\n                    {\r\n                        \"biomedicalConceptId\": \"BC11\",\r\n                        \"bcName\": \"Diastolic Blood Pressure \",\r\n                        \"bcSynonyms\": [\r\n                            \"DIABP\",\r\n                            \"DIA BP\",\r\n                            \"Blood pressure diastolic\",\r\n                            \"BP dias\",\r\n                            \"BP diastolic\",\r\n                            \"DBP\",\r\n                            \"Dias\",\r\n                            \"Dias BP\",\r\n                            \"Diast\",\r\n                            \"Diastoli \"\r\n                        ],\r\n                        \"bcReference\": \"href\",\r\n                        \"bcProperties\": [\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropId1\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": true,\r\n                                \"bcPropertyEnabled\": false,\r\n                                \"bcPropertyDataType\": \"strings\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"mmHg\"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"C25299\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Diastolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8462-4\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Diastolic Blood Pressure\"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            },\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropIds\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": false,\r\n                                \"bcPropertyEnabled\": true,\r\n                                \"bcPropertyDataType\": \"string\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"mmHg\"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"C25299\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Diastolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8462-4\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Diastolic Blood Pressure\"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            }\r\n                        ],\r\n                        \"bcConceptCode\": {\r\n                            \"aliasCodeId\": \"aliasId\",\r\n                            \"standardCode\": {\r\n                                \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                \"code\": \"C25299\",\r\n                                \"codeSystem\": \"http://www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-27\",\r\n                                \"decode\": \"Diastolic Blood Pressure\"\r\n                            },\r\n                            \"standardCodeAliases\": [\r\n                                {\r\n                                    \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                    \"code\": \"8462-4\",\r\n                                    \"codeSystem\": \"http://loinc.org/\",\r\n                                    \"codeSystemVersion\": \"2022-03-25\",\r\n                                    \"decode\": \"Diastolic Blood Pressure\"\r\n                                }\r\n                            ]\r\n                        }\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptId\": \"BC2\",\r\n                        \"bcName\": \"Systolic Blood Pressure\",\r\n                        \"bcSynonyms\": [\r\n                            \"SYSBP\",\r\n                            \"SYS BP\",\r\n                            \"Blood pressure systolic \"\r\n                        ],\r\n                        \"bcReference\": \"https://www.google.com\",\r\n                        \"bcProperties\": [\r\n                            {\r\n                                \"bcPropertyId\": \"BCPropId\",\r\n                                \"bcPropertyName\": \"Unit of Pressure\",\r\n                                \"bcPropertyRequired\": true,\r\n                                \"bcPropertyEnabled\": false,\r\n                                \"bcPropertyDataType\": \"string\",\r\n                                \"bcPropertyResponseCodes\": [\r\n                                    {\r\n                                        \"responseCodeId\": \"responseCodeId\",\r\n                                        \"responseCodeEnabled\": true,\r\n                                        \"code\": {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"C49670\",\r\n                                            \"codeSystem\": \"http://www.cdisc.org\",\r\n                                            \"codeSystemVersion\": \"2022-03-24\",\r\n                                            \"decode\": \"mmHg \"\r\n                                        }\r\n                                    }\r\n                                ],\r\n                                \"bcPropertyConceptCode\": {\r\n                                    \"aliasCodeId\": \"aliasId\",\r\n                                    \"standardCode\": {\r\n                                        \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                        \"code\": \"Cxxxx\",\r\n                                        \"codeSystem\": \"http://www.cdisc.org\",\r\n                                        \"codeSystemVersion\": \"2022-03-25\",\r\n                                        \"decode\": \"Systolic Blood Pressure\"\r\n                                    },\r\n                                    \"standardCodeAliases\": [\r\n                                        {\r\n                                            \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                            \"code\": \"8xx-x\",\r\n                                            \"codeSystem\": \"http://loinc.org/\",\r\n                                            \"codeSystemVersion\": \"2022-03-25\",\r\n                                            \"decode\": \"Systolic Blood Pressure \"\r\n                                        }\r\n                                    ]\r\n                                }\r\n                            }\r\n                        ],\r\n                        \"bcConceptCode\": {\r\n                            \"aliasCodeId\": \"aliasId\",\r\n                            \"standardCode\": {\r\n                                \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                \"code\": \"C25299\",\r\n                                \"codeSystem\": \"http://www.cdisc.org\",\r\n                                \"codeSystemVersion\": \"2022-03-25\",\r\n                                \"decode\": \"Diastolic Blood Pressure\"\r\n                            },\r\n                            \"standardCodeAliases\": [\r\n                                {\r\n                                    \"codeId\": \"1f287c91-5ad2-4211-a7b7-c329ff7dd429\",\r\n                                    \"code\": \"8462-4\",\r\n                                    \"codeSystem\": \"http://loinc.org/\",\r\n                                    \"codeSystemVersion\": \"2022-03-2\",\r\n                                    \"decode\": \"Diastolic Blood Pressure\"\r\n                                }\r\n                            ]\r\n                        }\r\n                    }\r\n                ],\r\n                \"bcCategories\": [\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC1\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC4\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC2\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Blood pressure\",\r\n                        \"bcCategoryDescription\": \"Blood pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC2\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC3\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Blood Pressure Tests\",\r\n                        \"bcCategoryDescription\": \"Blood Pressure Test\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC2\",\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC3\",\r\n                        \"bcCategoryParentIds\": [\r\n                            \"BCC2\"\r\n                        ],\r\n                        \"bcCategoryChildrenIds\": [],\r\n                        \"bcCategoryName\": \"Diastolic Blood Pressure\",\r\n                        \"bcCategoryDescription\": \"Diastolic Blood Pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC11\"\r\n                        ]\r\n                    },\r\n                    {\r\n                        \"biomedicalConceptCategoryId\": \"BCC4\",\r\n                        \"bcCategoryParentIds\": [],\r\n                        \"bcCategoryChildrenIds\": [\r\n                            \"BCC1\"\r\n                        ],\r\n                        \"bcCategoryName\": \"Systolic Blood Pressure\",\r\n                        \"bcCategoryDescription\": \"Systolic Blood Pressure\",\r\n                        \"bcCategoryMemberIds\": [\r\n                            \"BC2\"\r\n                        ]\r\n                    }\r\n                ],\r\n                \"bcSurrogates\": [\r\n                    {\r\n                        \"bcSurrogateId\": \"BCS1\",\r\n                        \"bcSurrogateName\": \"Diastolic Blood Pressure\",\r\n                        \"bcSurrogateDescription\": \"Diastolic Blood Pressure\",\r\n                        \"bcSurrogateReference\": \"https://google.com\"\r\n                    }\r\n                ]\r\n            }\r\n        ]\r\n    }\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{URL}}/v2/studydefinitions/{studyId}", "host": ["{{URL}}"], "path": ["v2", "studydefinitions", "{studyId}"]}}, "response": []}, {"name": "SoA", "request": {"method": "GET", "header": [{"key": "usdmVersion", "value": "1.9", "type": "default"}], "url": {"raw": "{{URL}}/v2/studydefinitions/{studyId}/studydesigns/soa", "host": ["{{URL}}"], "path": ["v2", "studydefinitions", "{studyId}", "studydesigns", "soa"], "query": [{"key": "sdruploadversion", "value": "1", "disabled": true}, {"key": "studydesignid", "value": "SD01", "disabled": true}, {"key": "studyWorkflowId", "value": "WF02", "disabled": true}]}}, "response": []}]}, {"name": "Common", "item": [{"name": "Common StudyHistory", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/studydefinitions/history", "host": ["{{URL}}"], "path": ["studydefinitions", "history"], "query": [{"key": "fromDate", "value": "2022-03-06", "disabled": true}, {"key": "toDate", "value": "2022-04-8", "disabled": true}, {"key": "studyTitle", "value": "Istory", "disabled": true}]}}, "response": []}, {"name": "Common AuditTrail", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/studydefinitions/{studyId}/audittrail", "host": ["{{URL}}"], "path": ["studydefinitions", "{studyId}", "audittrail"]}}, "response": []}, {"name": "Get API -> Usdm Mapping", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/versions", "host": ["{{URL}}"], "path": ["versions"]}}, "response": []}, {"name": "<PERSON>", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/studydefinitions/{studyId}/rawdata", "host": ["{{URL}}"], "path": ["studydefinitions", "{studyId}", "rawdata"], "query": [{"key": "sdruploadversion", "value": "3", "disabled": true}]}}, "response": []}, {"name": "GetChangeAudit", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/studydefinitions/{studyId}/changeaudit", "host": ["{{URL}}"], "path": ["studydefinitions", "{studyId}", "changeaudit"]}}, "response": []}, {"name": "eCPT", "request": {"method": "GET", "header": [], "url": {"raw": "{{URL}}/studydefinitions/{studyId}/studydesigns/ecpt", "host": ["{{URL}}"], "path": ["studydefinitions", "{studyId}", "studydesigns", "ecpt"], "query": [{"key": "sdruploadversion", "value": "3", "disabled": true}, {"key": "studydesignid", "value": "SD011", "disabled": true}]}}, "response": []}]}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token_}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": [""]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": [""]}}], "variable": [{"key": "URL", "value": "https://apim-sdr-demo-eastus.azure-api.net/api", "type": "default"}, {"key": "token_", "value": "<Enter Auth <PERSON>ken Here>", "type": "default"}]}