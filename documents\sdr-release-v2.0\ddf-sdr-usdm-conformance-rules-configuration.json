{"ConformanceRules": [{"usdmVersion": "1.9", "rules": [{"entity": "Activity", "required": ["Id", "ActivityDescription", "ActivityName", "ActivityIsConditionalReason", "ActivityIsConditional"]}, {"entity": "Address", "required": ["Text", "Line", "City", "District", "State", "PostalCode", "Country"]}, {"entity": "AliasCode", "required": ["Id", "StandardCode"]}, {"entity": "AnalysisPopulation", "required": ["Id", "PopulationDescription"]}, {"entity": "BiomedicalConcept", "required": ["Id", "BcName", "BcReference", "BcConceptCode"]}, {"entity": "BiomedicalConceptCategory", "required": ["Id", "BcCategoryName", "BcCategoryDescription"]}, {"entity": "BiomedicalConceptSurrogate", "required": ["Id", "BcSurrogateName", "BcSurrogateDescription", "BcSurrogateReference"]}, {"entity": "BiomedicalConceptProperty", "required": ["Id", "BcPropertyName", "BcPropertyRequired", "BcPropertyDataType", "BcPropertyEnabled", "BcPropertyConceptCode"]}, {"entity": "ClinicalStudy", "required": ["StudyTitle", "StudyType", "StudyIdentifiers", "StudyPhase", "StudyVersion", "StudyAcronym", "StudyRationale"]}, {"entity": "Code", "required": ["Id", "Code", "Decode", "CodeSystem", "CodeSystemVersion"]}, {"entity": "Encounter", "required": ["Id", "EncounterDescription", "EncounterName"]}, {"entity": "Endpoint", "required": ["Id", "EndpointDescription", "EndpointPurposeDescription"]}, {"entity": "Indication", "required": ["Id", "IndicationDescription"]}, {"entity": "InterCurrentEvents", "required": ["Id", "IntercurrentEventDescription", "IntercurrentEventName", "IntercurrentEventStrategy"]}, {"entity": "InvestigationalIntervention", "required": ["Id", "InterventionDescription"]}, {"entity": "Organisation", "required": ["Id", "OrganisationIdentifier", "OrganisationIdentifierScheme", "OrganisationName", "OrganisationType"]}, {"entity": "Procedure", "required": ["Id", "ProcedureType", "ProcedureCode", "ProcedureIsConditionalReason", "ProcedureIsConditional"]}, {"entity": "ResponseCode", "required": ["Id", "Code", "ResponseCodeEnabled"]}, {"entity": "StudyArm", "required": ["Id", "StudyArmDataOriginDescription", "StudyArmDataOriginType", "StudyArmDescription", "StudyArmType", "StudyArmName"]}, {"entity": "ScheduleTimelineExit", "required": ["Id"]}, {"entity": "ScheduledActivityInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduledDecisionInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduledInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduleTimelines", "required": ["Id", "ScheduleTimelineName", "ScheduleTimelineDescription", "EntryCondition"]}, {"entity": "StudyCells", "required": ["Id"]}, {"entity": "StudyDataCollection", "required": ["Id", "EcrfLink", "StudyDataName", "StudyDataDescription"]}, {"entity": "StudyDesignPopulation", "required": ["Id", "PopulationDescription", "PlannedNumberOfParticipants", "PlannedMaximumAgeOfParticipants", "PlannedMinimumAgeOfParticipants"]}, {"entity": "StudyDesign", "required": ["Id", "InterventionModel", "StudyDesignName", "StudyDesignDescription", "StudyDesignRationale"]}, {"entity": "StudyElements", "required": ["Id", "StudyElementDescription", "StudyElementName"]}, {"entity": "StudyEpoch", "required": ["Id", "StudyEpochName", "StudyEpochDescription", "StudyEpochType", "NextStudyEpochId", "PreviousStudyEpochId"]}, {"entity": "StudyEstimands", "required": ["Id", "SummaryMeasure", "AnalysisPopulation"]}, {"entity": "StudyIdentifiers", "required": ["Id", "StudyIdentifier", "StudyIdentifierScope"]}, {"entity": "Objective", "required": ["Id", "ObjectiveDescription"]}, {"entity": "StudyProtocolVersions", "required": ["Id", "<PERSON>rief<PERSON><PERSON><PERSON>", "OfficialTitle", "ProtocolEffectiveDate", "ProtocolStatus", "ProtocolVersion", "PublicTitle", "ScientificTitle"]}, {"entity": "Study", "required": ["ClinicalStudy"]}, {"entity": "Timing", "required": ["Id", "TimingType", "<PERSON>ing<PERSON><PERSON><PERSON>", "RelativeFromScheduledInstanceId", "RelativeToScheduledInstanceId", "<PERSON>ingW<PERSON>ow", "TimingRelativeToFrom"]}, {"entity": "TransitionRule", "required": ["Id", "TransitionRuleDescription"]}, {"entity": "WorkflowItem", "required": ["Id", "WorkflowItemDescription"]}, {"entity": "Workflow", "required": ["Id", "WorkflowDescription"]}]}]}