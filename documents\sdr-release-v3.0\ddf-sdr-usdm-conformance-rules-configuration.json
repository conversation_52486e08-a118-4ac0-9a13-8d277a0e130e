{"ConformanceRules": [{"usdmVersion": "1.9", "rules": [{"entity": "Activity", "required": ["Id", "ActivityDescription", "ActivityName", "ActivityIsConditionalReason", "ActivityIsConditional"]}, {"entity": "Address", "required": ["Text", "Line", "City", "District", "State", "PostalCode", "Country"]}, {"entity": "AliasCode", "required": ["Id", "StandardCode"]}, {"entity": "AnalysisPopulation", "required": ["Id", "PopulationDescription"]}, {"entity": "BiomedicalConcept", "required": ["Id", "BcName", "BcReference", "BcConceptCode"]}, {"entity": "BiomedicalConceptCategory", "required": ["Id", "BcCategoryName", "BcCategoryDescription"]}, {"entity": "BiomedicalConceptSurrogate", "required": ["Id", "BcSurrogateName", "BcSurrogateDescription", "BcSurrogateReference"]}, {"entity": "BiomedicalConceptProperty", "required": ["Id", "BcPropertyName", "BcPropertyRequired", "BcPropertyDataType", "BcPropertyEnabled", "BcPropertyConceptCode"]}, {"entity": "Study", "required": ["StudyTitle", "StudyType", "StudyIdentifiers", "StudyPhase", "StudyVersion", "StudyAcronym", "StudyRationale"]}, {"entity": "Code", "required": ["Id", "Code", "Decode", "CodeSystem", "CodeSystemVersion"]}, {"entity": "Encounter", "required": ["Id", "EncounterDescription", "EncounterName"]}, {"entity": "Endpoint", "required": ["Id", "EndpointDescription", "EndpointPurposeDescription"]}, {"entity": "Indication", "required": ["Id", "IndicationDescription"]}, {"entity": "InterCurrentEvents", "required": ["Id", "IntercurrentEventDescription", "IntercurrentEventName", "IntercurrentEventStrategy"]}, {"entity": "InvestigationalIntervention", "required": ["Id", "InterventionDescription"]}, {"entity": "Organisation", "required": ["Id", "OrganisationIdentifier", "OrganisationIdentifierScheme", "OrganisationName", "OrganisationType"]}, {"entity": "Procedure", "required": ["Id", "ProcedureType", "ProcedureCode", "ProcedureIsConditionalReason", "ProcedureIsConditional"]}, {"entity": "ResponseCode", "required": ["Id", "Code", "ResponseCodeEnabled"]}, {"entity": "StudyArm", "required": ["Id", "StudyArmDataOriginDescription", "StudyArmDataOriginType", "StudyArmDescription", "StudyArmType", "StudyArmName"]}, {"entity": "ScheduleTimelineExit", "required": ["Id"]}, {"entity": "ScheduledActivityInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduledDecisionInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduledInstance", "required": ["Id", "ScheduleSequenceNumber", "ScheduledInstanceType"]}, {"entity": "ScheduleTimelines", "required": ["Id", "ScheduleTimelineName", "ScheduleTimelineDescription", "EntryCondition"]}, {"entity": "StudyCells", "required": ["Id"]}, {"entity": "StudyDataCollection", "required": ["Id", "EcrfLink", "StudyDataName", "StudyDataDescription"]}, {"entity": "StudyDesignPopulation", "required": ["Id", "PopulationDescription", "PlannedNumberOfParticipants", "PlannedMaximumAgeOfParticipants", "PlannedMinimumAgeOfParticipants"]}, {"entity": "StudyDesign", "required": ["Id", "InterventionModel", "StudyDesignName", "StudyDesignDescription", "StudyDesignRationale"]}, {"entity": "StudyElements", "required": ["Id", "StudyElementDescription", "StudyElementName"]}, {"entity": "StudyEpoch", "required": ["Id", "StudyEpochName", "StudyEpochDescription", "StudyEpochType", "NextStudyEpochId", "PreviousStudyEpochId"]}, {"entity": "StudyEstimands", "required": ["Id", "SummaryMeasure", "AnalysisPopulation"]}, {"entity": "StudyIdentifiers", "required": ["Id", "StudyIdentifier", "StudyIdentifierScope"]}, {"entity": "Objective", "required": ["Id", "ObjectiveDescription"]}, {"entity": "StudyProtocolVersions", "required": ["Id", "<PERSON>rief<PERSON><PERSON><PERSON>", "OfficialTitle", "ProtocolEffectiveDate", "ProtocolStatus", "ProtocolVersion", "PublicTitle", "ScientificTitle"]}, {"entity": "StudyDefinitions", "required": ["Study"]}, {"entity": "Timing", "required": ["Id", "TimingType", "<PERSON>ing<PERSON><PERSON><PERSON>", "RelativeFromScheduledInstanceId", "RelativeToScheduledInstanceId", "<PERSON>ingW<PERSON>ow", "TimingRelativeToFrom"]}, {"entity": "TransitionRule", "required": ["Id", "TransitionRuleDescription"]}, {"entity": "WorkflowItem", "required": ["Id", "WorkflowItemDescription"]}, {"entity": "Workflow", "required": ["Id", "WorkflowDescription"]}]}, {"usdmVersion": "2.0", "rules": [{"entity": "Activity", "required": ["Id", "ActivityName", "ActivityIsConditional"]}, {"entity": "Address", "required": ["Id", "Text", "Line", "City", "District", "State", "PostalCode", "Country"]}, {"entity": "AliasCode", "required": ["Id", "StandardCode"]}, {"entity": "AnalysisPopulation", "required": ["Id", "PopulationDescription"]}, {"entity": "BiomedicalConcept", "required": ["Id", "BcName", "BcReference", "BcConceptCode"]}, {"entity": "BiomedicalConceptCategory", "required": ["Id", "BcCategoryName"]}, {"entity": "BiomedicalConceptSurrogate", "required": ["Id", "BcSurrogateName", "BcSurrogateReference"]}, {"entity": "BiomedicalConceptProperty", "required": ["Id", "BcPropertyName", "BcPropertyRequired", "BcPropertyDataType", "BcPropertyEnabled", "BcPropertyConceptCode"]}, {"entity": "Study", "required": ["StudyTitle", "StudyType", "StudyIdentifiers", "StudyPhase", "StudyVersion", "StudyAcronym", "StudyRationale"]}, {"entity": "Code", "required": ["Id", "Code", "Decode", "CodeSystem", "CodeSystemVersion"]}, {"entity": "Encounter", "required": ["Id", "EncounterName"]}, {"entity": "Endpoint", "required": ["Id", "EndpointDescription", "EndpointPurposeDescription"]}, {"entity": "Indication", "required": ["Id", "IndicationDescription"]}, {"entity": "InterCurrentEvents", "required": ["Id", "IntercurrentEventName", "IntercurrentEventStrategy"]}, {"entity": "InvestigationalIntervention", "required": ["Id", "InterventionDescription"]}, {"entity": "Organisation", "required": ["Id", "OrganisationIdentifier", "OrganisationIdentifierScheme", "OrganisationName", "OrganisationType"]}, {"entity": "Procedure", "required": ["Id", "ProcedureName", "ProcedureType", "ProcedureCode", "ProcedureIsConditional"]}, {"entity": "ResponseCode", "required": ["Id", "Code", "ResponseCodeEnabled"]}, {"entity": "StudyArm", "required": ["Id", "StudyArmDataOriginDescription", "StudyArmDataOriginType", "StudyArmType", "StudyArmName"]}, {"entity": "ScheduleTimelineExit", "required": ["Id"]}, {"entity": "ScheduledActivityInstance", "required": ["Id", "ScheduledInstanceType"]}, {"entity": "ScheduledDecisionInstance", "required": ["Id", "ScheduledInstanceType"]}, {"entity": "ScheduledInstance", "required": ["Id", "ScheduledInstanceType"]}, {"entity": "ScheduleTimelines", "required": ["Id", "ScheduleTimelineName", "EntryCondition", "MainTimeline"]}, {"entity": "StudyCells", "required": ["Id", "StudyArmId", "StudyEpochId"]}, {"entity": "StudyDataCollection", "required": ["Id", "EcrfLink", "StudyDataName", "StudyDataDescription"]}, {"entity": "StudyDesignPopulation", "required": ["Id", "PopulationDescription", "PlannedNumberOfParticipants", "PlannedMaximumAgeOfParticipants", "PlannedMinimumAgeOfParticipants"]}, {"entity": "StudyDesign", "required": ["Id", "InterventionModel", "StudyDesignName", "StudyDesignRationale", "StudyCells", "StudyArms", "StudyEpochs"]}, {"entity": "StudyElements", "required": ["Id", "StudyElementName"]}, {"entity": "StudyEpoch", "required": ["Id", "StudyEpochName", "StudyEpochType"]}, {"entity": "StudyEstimands", "required": ["Id", "SummaryMeasure", "AnalysisPopulation"]}, {"entity": "StudyIdentifiers", "required": ["Id", "StudyIdentifier", "StudyIdentifierScope"]}, {"entity": "Objective", "required": ["Id", "ObjectiveDescription"]}, {"entity": "StudyProtocolVersions", "required": ["Id", "<PERSON>rief<PERSON><PERSON><PERSON>", "OfficialTitle", "ProtocolEffectiveDate", "ProtocolStatus", "ProtocolVersion", "PublicTitle", "ScientificTitle"]}, {"entity": "StudyDefinitions", "required": ["Study"]}, {"entity": "Timing", "required": ["Id", "TimingType", "<PERSON>ing<PERSON><PERSON><PERSON>", "TimingRelativeToFrom"]}, {"entity": "TransitionRule", "required": ["Id", "TransitionRuleDescription"]}]}, {"usdmVersion": "3.0", "rules": [{"entity": "Activity", "required": ["Id", "IsConditional", "Name", "InstanceType"]}, {"entity": "Address", "required": ["Id", "InstanceType"]}, {"entity": "AdministrationDuration", "required": ["Description", "DurationWillVary", "Id", "InstanceType", "ReasonDurationWillVary"]}, {"entity": "AgentAdministration", "required": ["<PERSON><PERSON>", "Duration", "Frequency", "Id", "InstanceType", "Name", "Route"]}, {"entity": "AliasCode", "required": ["Id", "InstanceType", "StandardCode"]}, {"entity": "AnalysisPopulation", "required": ["Id", "InstanceType", "Name", "Text"]}, {"entity": "BiomedicalConcept", "required": ["Code", "Id", "InstanceType", "Name", "Reference"]}, {"entity": "BiomedicalConceptCategory", "required": ["Id", "InstanceType", "Name"]}, {"entity": "BiomedicalConceptProperty", "required": ["Code", "DataType", "IsEnabled", "Id", "InstanceType", "Name", "IsRequired"]}, {"entity": "BiomedicalConceptSurrogate", "required": ["Id", "InstanceType", "Name"]}, {"entity": "Characteristic", "required": ["Id", "InstanceType", "Name", "Text"]}, {"entity": "Code", "required": ["Code", "CodeSystem", "CodeSystemVersion", "Decode", "Id", "InstanceType"]}, {"entity": "Condition", "required": ["Id", "InstanceType", "Name", "Text"]}, {"entity": "ConditionAssignment", "required": ["Id", "InstanceType", "Condition", "ConditionTargetId"]}, {"entity": "EligibilityCriterion", "required": ["Category", "Id", "InstanceType", "Identifier", "InstanceType", "Name", "Text"]}, {"entity": "Encounter", "required": ["Id", "InstanceType", "Name"]}, {"entity": "Endpoint", "required": ["Id", "InstanceType", "Level", "Name", "Text"]}, {"entity": "<PERSON><PERSON><PERSON><PERSON>", "required": ["AnalysisPopulation", "Id", "InstanceType", "IntercurrentEvents", "SummaryMeasure", "InterventionId", "VariableOfInterestId"]}, {"entity": "GeographicScope", "required": ["Id", "InstanceType", "Type"]}, {"entity": "GovernanceDate", "required": ["DateValue", "Id", "InstanceType", "Type", "Name", "GeographicScopes"]}, {"entity": "Indication", "required": ["Id", "InstanceType", "Name"]}, {"entity": "IntercurrentEvent", "required": ["Id", "InstanceType", "Name", "Strategy"]}, {"entity": "Masking", "required": ["Id", "InstanceType", "Role"]}, {"entity": "NarrativeContent", "required": ["Id", "InstanceType", "Name", "SectionNumber", "SectionTitle"]}, {"entity": "Objective", "required": ["Id", "InstanceType", "Name", "Text", "Level"]}, {"entity": "Organization", "required": ["Id", "InstanceType", "Identifier", "IdentifierScheme", "Name", "OrganizationType"]}, {"entity": "ParameterMap", "required": ["Id", "InstanceType", "Tag", "Reference"]}, {"entity": "Procedure", "required": ["Code", "Id", "InstanceType", "IsConditional", "Name", "Type"]}, {"entity": "Quantity", "required": ["Id", "InstanceType", "Value"]}, {"entity": "ResearchOrganization", "required": ["Id", "InstanceType", "Identifier", "IdentifierScheme", "Name", "OrganizationType", "Manages"]}, {"entity": "ResponseCode", "required": ["Code", "Id", "InstanceType", "IsEnabled"]}, {"entity": "Range", "required": ["IsApproximate", "Id", "InstanceType", "MinValue", "MaxValue"]}, {"entity": "ScheduleTimelineExit", "required": ["Id", "InstanceType"]}, {"entity": "ScheduleTimelines", "required": ["EntryCondition", "Id", "InstanceType", "MainTimeline", "Name"]}, {"entity": "ScheduledActivityInstance", "required": ["Id", "Name", "InstanceType"]}, {"entity": "ScheduledDecisionInstance", "required": ["Id", "Name", "InstanceType", "ConditionAssignments"]}, {"entity": "ScheduledInstance", "required": ["Id", "InstanceType"]}, {"entity": "Study", "required": ["Name", "InstanceType"]}, {"entity": "StudyAmendment", "required": ["Number", "Summary", "Id", "InstanceType", "Enrollments", "SubstantialImpact", "PrimaryReason"]}, {"entity": "StudyAmendmentReason", "required": ["Code", "Id", "InstanceType"]}, {"entity": "StudyArm", "required": ["DataOriginDescription", "DataOriginType", "Id", "InstanceType", "Name", "Type"]}, {"entity": "StudyCell", "required": ["ArmId", "EpochId", "Id", "InstanceType"]}, {"entity": "StudyCohort", "required": ["Name", "Id", "InstanceType", "Criteria", "IncludesHealthySubjects"]}, {"entity": "StudyDefinitions", "required": ["Study", "UsdmVersion"]}, {"entity": "StudyDesign", "required": ["Arms", "Epochs", "Id", "InstanceType", "InterventionModel", "Name", "Rationale", "StudyCells"]}, {"entity": "StudyDesignPopulation", "required": ["Id", "InstanceType", "Name", "Criteria", "IncludesHealthySubjects"]}, {"entity": "StudyElements", "required": ["Id", "InstanceType", "Name"]}, {"entity": "StudyEpoch", "required": ["Id", "InstanceType", "Name", "Type"]}, {"entity": "StudyIdentifier", "required": ["Id", "InstanceType", "StudyIdentifier", "StudyIdentifierScope"]}, {"entity": "StudyIntervention", "required": ["Codes", "Id", "InstanceType", "MinimumResponseDuration", "Name", "PharmacologicClass", "ProductDesignation", "Role", "Type"]}, {"entity": "StudyProtocolDocument", "required": ["Name", "Id", "InstanceType"]}, {"entity": "StudyProtocolDocumentVersion", "required": ["<PERSON>rief<PERSON><PERSON><PERSON>", "Id", "InstanceType", "OfficialTitle", "ProtocolStatus", "ProtocolVersion", "PublicTitle", "ScientificTitle"]}, {"entity": "StudySite", "required": ["Id", "InstanceType", "Name"]}, {"entity": "StudyTitle", "required": ["Id", "InstanceType", "Text", "Type"]}, {"entity": "StudyVersion", "required": ["Id", "InstanceType", "StudyIdentifiers", "StudyPhase", "Rationale", "Titles", "StudyType", "VersionIdentifier"]}, {"entity": "SubjectEnrollment", "required": ["Id", "InstanceType", "Type"]}, {"entity": "SyntaxTemplate", "required": ["DictionaryId", "Id", "InstanceType", "Name", "Text"]}, {"entity": "SyntaxTemplateDectionary", "required": ["Id", "InstanceType", "Name", "ParameterMap"]}, {"entity": "Timing", "required": ["Id", "InstanceType", "Name", "RelativeToFrom", "Type", "Value", "ValueLabel"]}, {"entity": "TransitionRule", "required": ["Id", "InstanceType", "Name", "Text"]}]}]}