using System.Collections.Generic;

namespace TransCelerate.SDR.Core.Entities.StudyV5
{
    [MongoDB.Bson.Serialization.Attributes.BsonIgnoreExtraElements]
    [MongoDB.Bson.Serialization.Attributes.BsonNoId]
    public class MedicalDeviceEntity : IId
    {
        public string Id { get; set; }
        public string InstanceType { get; set; }
        public string Name { get; set; }
        public string Description { get; set; }
        public string Label { get; set; }
        public string HardwareVersion { get; set; }
        public string SoftwareVersion { get; set; }
        public AdministrableProductEntity EmbeddedProduct { get; set; }
        public CodeEntity Sourcing { get; set; }
        public CommentAnnotationEntity Notes { get; set; }
        public List<MedicalDeviceIdentifierEntity> Identifiers { get; set; }
    }
}
