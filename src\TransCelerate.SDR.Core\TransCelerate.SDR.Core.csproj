﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
	  <NoWarn>$(NoWarn);1591</NoWarn>
	  <DocumentationMarkdown>$(MSBuildProjectDirectory)\TransCelerate.SDR.Core.md</DocumentationMarkdown>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" Version="2.3.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="9.0.1" />
    <PackageReference Include="Microsoft.Extensions.Primitives" Version="9.0.1" />
    <PackageReference Include="MongoDB.Driver" Version="2.19.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="ObjectsComparer" Version="1.4.1" />
    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Handles" Version="4.3.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="Vsxmd" Version="1.4.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>
