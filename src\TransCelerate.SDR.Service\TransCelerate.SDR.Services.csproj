﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
	  <NoWarn>$(NoWarn);1591</NoWarn>
	  <DocumentationMarkdown>$(MSBuildProjectDirectory)\TransCelerate.SDR.Services.md</DocumentationMarkdown>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" Version="9.0.1" />
    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Handles" Version="4.3.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="Vsxmd" Version="1.4.6">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TransCelerate.SDR.Core\TransCelerate.SDR.Core.csproj" />
    <ProjectReference Include="..\TransCelerate.SDR.DataAccess\TransCelerate.SDR.DataAccess.csproj" />
  </ItemGroup>

</Project>
