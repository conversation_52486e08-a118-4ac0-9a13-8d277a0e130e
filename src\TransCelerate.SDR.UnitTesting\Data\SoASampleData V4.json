{"activities": [{"id": "ACT001", "name": "Informed consent", "label": "name", "description": "Informed consents", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT002", "previousId": "", "isConditional": false, "isConditionalReason": "Reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT002", "name": "Eligibility criteria", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT003", "previousId": "ACT001", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT003", "name": "Demography", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT004", "previousId": "ACT002", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT004", "name": "Medical history", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT005", "previousId": "ACT003", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT005", "name": "Disease characteristics", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT006", "previousId": "ACT004", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT006", "name": "Physical exam", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT007", "previousId": "ACT005", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT007", "name": "Height", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT008", "previousId": "ACT006", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT008", "name": "12-lead ECG", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT009", "previousId": "ACT007", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT009", "name": "Hematology (predose)", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT010", "previousId": "ACT008", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT010", "name": "Chemistry (predose)", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT011", "previousId": "ACT009", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT011", "name": "Serology", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT012", "previousId": "ACT010", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT012", "name": "Urinalysis", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT013", "previousId": "ACT011", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT013", "name": "Pregnancy test", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT014", "previousId": "ACT012", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT014", "name": "Ensure availability of medication X", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT015", "previousId": "ACT013", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT015", "name": "Hospitalization", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT016", "previousId": "ACT014", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT016", "name": "Weight", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT017", "previousId": "ACT015", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT017", "name": "Vital signs", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT018", "previousId": "ACT016", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT018", "name": "adverse events", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "ACT019", "previousId": "ACT017", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}, {"id": "ACT019", "name": "Concomitant medications", "label": "name", "description": "Informed consent", "definedProcedures": [{"id": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "isConditional": false, "isConditionalReason": "Reasons", "studyInterventionId": "interventionId"}, {"id": "f4e2", "name": "Name", "label": "name", "description": "Desc", "procedureType": "Specimen Collection", "code": {"id": "40c6a10a-da80-4174-928c-88124a5f8e76", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell count"}, "isConditional": true, "isConditionalReason": "Reason", "studyInterventionId": "interventionId"}], "nextId": "", "previousId": "ACT018", "isConditional": false, "isConditionalReason": "reason", "bcCategoryIds": ["BCC1"], "bcSurrogateIds": ["BCS1"], "biomedicalConceptIds": ["BC11"], "timelineId": "Timeline01"}], "encounters": [{"id": "VIS11", "name": "SCREENING VISIT", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure"}, "type": {"id": "1f287c91-5ad2-4211-a7b7-c329ff7dd429", "code": "C25299", "codeSystem": "http://www.cdisc.org", "codeSystemVersion": "2022-03-25", "decode": "Diastolic Blood Pressure"}, "nextId": "VIS12", "previousId": "", "scheduledAtTimingId": "T030", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS12", "name": "RUN-IN VISIT 1", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS13", "previousId": "VIS11", "scheduledAtTimingId": "T02", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS13", "name": "RUN-IN VISIT 2", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS14", "previousId": "VIS12", "scheduledAtTimingId": "T03", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS14", "name": "RUN-IN VISIT 3", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS15", "previousId": "VIS13", "scheduledAtTimingId": "T04", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS15", "name": "CYCLE 1, TREATMENT 1", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS16", "previousId": "VIS14", "scheduledAtTimingId": "T03", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS16", "name": "CYCLE 1, TREATMENT 2", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS17", "previousId": "VIS15", "scheduledAtTimingId": "T06", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS17", "name": "CYCLE 1, TREATMENT 3", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS18", "previousId": "VIS16", "scheduledAtTimingId": "T07", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS18", "name": "CYCLE 2, TREATMENT 1", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS19", "previousId": "VIS17", "scheduledAtTimingId": "T08", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS19", "name": "CYCLE 2, TREATMENT 2", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS20", "previousId": "VIS18", "scheduledAtTimingId": "T09", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS20", "name": "CYCLE 3, TREATMENT 1", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS21", "previousId": "VIS19", "scheduledAtTimingId": "T10", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS21", "name": "CYCLE 3, TREATMENT 2", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS22", "previousId": "VIS20", "scheduledAtTimingId": "T11", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS22", "name": "FU 1", "label": "name", "description": "PLANNED VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "VIS23", "previousId": "VIS21", "scheduledAtTimingId": "T16", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}, {"id": "VIS23", "name": "FU 2", "label": "name", "description": "VIRTUAL VISIT", "contactModes": [{"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}], "environmentalSetting": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "type": {"id": "40c6a10a-da80-41746", "code": "767002", "codeSystem": "SNOMED-CT", "codeSystemVersion": "2022-05-31", "decode": "White blood cell counts"}, "nextId": "", "previousId": "VIS22", "scheduledAtTimingId": "T17", "transitionStartRule": {"id": "14a6b9fa-f080-415b-83bd-6d6ffe5e4b60", "name": "name", "label": "name", "description": "Start Rule", "text": "text"}, "transitionEndRule": {"id": "ae648ac6-f2c2-49d8-8ee2-64ccadec513e", "name": "name", "label": "name", "description": "End Rule", "text": "text"}}], "studyScheduleTimelines": [{"Id": "Timeline01", "scheduleTimelineName": "name", "scheduleTimelineDescription": "Timeline for Stage III", "entryCondition": "condition", "scheduleTimelineEntryId": "INS001", "scheduleTimelineExits": [{"scheduleTimelineExitId": "Exit01"}], "scheduleTimelines": [{"id": "Timeline01", "name": "name", "label": "label", "description": "Timeline for Stage III", "entryCondition": "condition", "entryId": "INS001", "exits": [{"id": "Exit01"}], "mainTimeline": true, "instances": [{"instanceType": "ACTIVITY", "encounterId": "VIS11", "activityIds": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"], "id": "INS002", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS003", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T01", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 1", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"id": "T02", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "value": "Day 15", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"instanceType": "DECISION", "conditionAssignments": {"option": "changes"}, "id": "INS001", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS002", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d", "timings": [{"id": "T030", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 1", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"id": "T04", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "value": "Day 15", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS12", "activityIds": ["ACT003", "ACT002"], "id": "INS003", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS004", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T03", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 1", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"id": "T04", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "value": "Day 15", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS13", "activityIds": ["ACT005", "ACT006"], "id": "INS004", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS005", "epochId": "", "timings": [{"id": "T06", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 10", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS14", "activityIds": ["ACT006", "ACT007"], "id": "INS005", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS006", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T07", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 18", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS15", "activityIds": ["ACT008", "ACT009"], "id": "INS006", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS007", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T08", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 20", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS16", "activityIds": ["ACT010", "ACT011"], "id": "INS007", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS008", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T09", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 23", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS17", "activityIds": ["ACT012", "ACT013"], "id": "INS008", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS009", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T10", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 25", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS18", "activityIds": ["ACT013", "ACT014"], "id": "INS009", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS010", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T11", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 30", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS19", "activityIds": ["ACT015", "ACT016"], "id": "INS010", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS011", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T12", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "Day 35", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS20", "activityIds": ["ACT017", "ACT018"], "id": "INS011", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS012", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T13", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "WEEK 1", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS21", "activityIds": ["ACT018", "ACT019"], "id": "INS012", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS013", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T14", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "WEEK 3", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS22", "activityIds": ["ACT019", "ACT002"], "id": "INS013", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS014", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T15", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "WEEK 5", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS23", "activityIds": ["ACT003", "ACT001"], "id": "INS014", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "INS015", "epochId": "32616f8e-aee7-4038-9d6f-ed2adb8879d9", "timings": [{"id": "T16", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "WEEK 6", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"instanceType": "ACTIVITY", "encounterId": "VIS23", "activityIds": ["ACT005", "ACT002"], "id": "INS015", "timelineExitId": "Exit01", "timelineId": "Timeline01", "defaultConditionId": "", "epochId": "", "timings": [{"id": "T17", "type": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "value": "MONTH 1", "description": "desc", "window": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "windowLower": "lower", "windowUpper": "upper", "relativeToFrom": {"id": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}]}]}], "studyId": "c2254c40-a085-4ae1-bcdc-be61cd88760a", "studyTitle": "Study of Stomach Cancer", "studyDesigns": [{"studyDesignId": "SD01", "studyDesignName": "Design for Lung Cancer", "studyDesignDescription": "Stage III Lung Cancer", "studyScheduleTimelines": [{"scheduleTimelineId": "Timeline01", "scheduleTimelineName": "name", "scheduleTimelineDescription": "Timeline for Stage III", "entryCondition": "condition", "scheduleTimelineSoA": {"orderOfActivities": [{"activityId": "ACT001", "description": "Informed consents", "name": "Informed consent", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "Reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT002", "description": "Informed consent", "name": "Eligibility criteria", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT003", "description": "Informed consent", "name": "Demography", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT005", "description": "Informed consent", "name": "Disease characteristics", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT006", "description": "Informed consent", "name": "Physical exam", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT007", "description": "Informed consent", "name": "Height", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT008", "description": "Informed consent", "name": "12-lead ECG", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT009", "description": "Informed consent", "name": "Hematology (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT010", "description": "Informed consent", "name": "Chemistry (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT011", "description": "Informed consent", "name": "Serology", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT012", "description": "Informed consent", "name": "Urinalysis", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT013", "description": "Informed consent", "name": "Pregnancy test", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT014", "description": "Informed consent", "name": "Ensure availability of medication X", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT015", "description": "Informed consent", "name": "Hospitalization", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT016", "description": "Informed consent", "name": "Weight", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT017", "description": "Informed consent", "name": "Vital signs", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT018", "description": "Informed consent", "name": "adverse events", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT019", "description": "Informed consent", "name": "Concomitant medications", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}], "SoA": [{"encounterId": "VIS11", "encounterName": "SCREENING VISIT", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}]}, {"encounterId": "VIS12", "encounterName": "RUN-IN VISIT 1", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT002"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT003", "ACT002"]}]}, {"encounterId": "VIS13", "encounterName": "RUN-IN VISIT 2", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 10", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT006"]}]}, {"encounterId": "VIS14", "encounterName": "RUN-IN VISIT 3", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 18", "timingWindow": "window", "timingType": "After", "activities": ["ACT006", "ACT007"]}]}, {"encounterId": "VIS15", "encounterName": "CYCLE 1, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 20", "timingWindow": "window", "timingType": "After", "activities": ["ACT008", "ACT009"]}]}, {"encounterId": "VIS16", "encounterName": "CYCLE 1, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 10", "timings": [{"timingValue": "Day 23", "timingWindow": "window", "timingType": "After", "activities": ["ACT010", "ACT011"]}]}, {"encounterId": "VIS17", "encounterName": "CYCLE 1, TREATMENT 3", "encounterScheduledAtTimingValue": "Day 18", "timings": [{"timingValue": "Day 25", "timingWindow": "window", "timingType": "After", "activities": ["ACT012", "ACT013"]}]}, {"encounterId": "VIS18", "encounterName": "CYCLE 2, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 20", "timings": [{"timingValue": "Day 30", "timingWindow": "window", "timingType": "After", "activities": ["ACT013", "ACT014"]}]}, {"encounterId": "VIS19", "encounterName": "CYCLE 2, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 23", "timings": [{"timingValue": "Day 35", "timingWindow": "window", "timingType": "After", "activities": ["ACT015", "ACT016"]}]}, {"encounterId": "VIS20", "encounterName": "CYCLE 3, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 25", "timings": [{"timingValue": "WEEK 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT017", "ACT018"]}]}, {"encounterId": "VIS21", "encounterName": "CYCLE 3, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 30", "timings": [{"timingValue": "WEEK 3", "timingWindow": "window", "timingType": "After", "activities": ["ACT018", "ACT019"]}]}, {"encounterId": "VIS22", "encounterName": "FU 1", "encounterScheduledAtTimingValue": "WEEK 6", "timings": [{"timingValue": "WEEK 5", "timingWindow": "window", "timingType": "After", "activities": ["ACT019", "ACT002"]}]}, {"encounterId": "VIS23", "encounterName": "FU 2", "encounterScheduledAtTimingValue": "MONTH 1", "timings": [{"timingValue": "WEEK 6", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT001"]}, {"timingValue": "MONTH 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT002"]}]}]}}]}]}