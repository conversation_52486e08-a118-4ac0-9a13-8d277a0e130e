{"activities": [{"Id": "ACT001", "activityDescription": "Informed consent", "activityName": "Informed consent", "definedProcedures": null, "nextActivityId": "ACT002", "previousActivityId": null, "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT002", "activityDescription": "Informed consent", "activityName": "Eligibility criteria", "definedProcedures": null, "nextActivityId": "ACT003", "previousActivityId": "ACT001", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT003", "activityDescription": "Informed consent", "activityName": "Demography", "definedProcedures": null, "nextActivityId": "ACT004", "previousActivityId": "ACT002", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT004", "activityDescription": "Informed consent", "activityName": "Medical history", "definedProcedures": null, "nextActivityId": "ACT005", "previousActivityId": "ACT003", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT005", "activityDescription": "Informed consent", "activityName": "Disease characteristics", "definedProcedures": null, "nextActivityId": "ACT006", "previousActivityId": "ACT004", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT006", "activityDescription": "Informed consent", "activityName": "Physical exam", "definedProcedures": null, "nextActivityId": "ACT007", "previousActivityId": "ACT005", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT007", "activityDescription": "Informed consent", "activityName": "Height", "definedProcedures": null, "nextActivityId": "ACT008", "previousActivityId": "ACT006", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT008", "activityDescription": "Informed consent", "activityName": "12-lead ECG", "definedProcedures": null, "nextActivityId": "ACT009", "previousActivityId": "ACT007", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT009", "activityDescription": "Informed consent", "activityName": "Hematology (predose)", "definedProcedures": null, "nextActivityId": "ACT010", "previousActivityId": "ACT008", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT010", "activityDescription": "Informed consent", "activityName": "Chemistry (predose)", "definedProcedures": null, "nextActivityId": "ACT011", "previousActivityId": "ACT009", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT011", "activityDescription": "Informed consent", "activityName": "Serology", "definedProcedures": null, "nextActivityId": "ACT012", "previousActivityId": "ACT010", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT012", "activityDescription": "Informed consent", "activityName": "Urinalysis", "definedProcedures": null, "nextActivityId": "ACT013", "previousActivityId": "ACT011", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT013", "activityDescription": "Informed consent", "activityName": "Pregnancy test", "definedProcedures": null, "nextActivityId": "ACT014", "previousActivityId": "ACT012", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT014", "activityDescription": "Informed consent", "activityName": "Ensure availability of medication X", "definedProcedures": null, "nextActivityId": "ACT015", "previousActivityId": "ACT013", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT015", "activityDescription": "Informed consent", "activityName": "Hospitalization", "definedProcedures": null, "nextActivityId": "ACT016", "previousActivityId": "ACT014", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT016", "activityDescription": "Informed consent", "activityName": "Weight", "definedProcedures": null, "nextActivityId": "ACT017", "previousActivityId": "ACT015", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT017", "activityDescription": "Informed consent", "activityName": "Vital signs", "definedProcedures": null, "nextActivityId": "ACT018", "previousActivityId": "ACT016", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT018", "activityDescription": "Informed consent", "activityName": "adverse events", "definedProcedures": null, "nextActivityId": "ACT019", "previousActivityId": "ACT017", "studyDataCollection": null, "activityIsOptional": false}, {"Id": "ACT019", "activityDescription": "Informed consent", "activityName": "Concomitant medications", "definedProcedures": null, "nextActivityId": null, "previousActivityId": "ACT018", "studyDataCollection": null, "activityIsOptional": false}], "encounters": [{"Id": "VIS11", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "SCREENING VISIT", "encounterType": null, "nextEncounterId": "VIS12", "previousEncounterId": null, "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS12", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "RUN-IN VISIT 1", "encounterType": null, "nextEncounterId": "VIS13", "previousEncounterId": "VIS11", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS13", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "RUN-IN VISIT 2", "encounterType": null, "nextEncounterId": "VIS14", "previousEncounterId": "VIS12", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS14", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "RUN-IN VISIT 3", "encounterType": null, "nextEncounterId": "VIS15", "previousEncounterId": "VIS13", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS15", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE 1, TREATMENT DAY 1", "encounterType": null, "nextEncounterId": "VIS16", "previousEncounterId": "VIS14", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS16", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE 1, TREATMENT DAY 3", "encounterType": null, "nextEncounterId": "VIS17", "previousEncounterId": "VIS15", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS17", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE 1, TREATMENT DAY 5", "encounterType": null, "nextEncounterId": "VIS18", "previousEncounterId": "VIS16", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS18", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE 2, TREATMENT DAY 1", "encounterType": null, "nextEncounterId": "VIS19", "previousEncounterId": "VIS17", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS19", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE 2, TREATMENT DAY 3", "encounterType": null, "nextEncounterId": "VIS20", "previousEncounterId": "VIS18", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS20", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE X, TREATMENT DAY 1", "encounterType": null, "nextEncounterId": "VIS21", "previousEncounterId": "VIS19", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS21", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "CYCLE X, TREATMENT DAY 4", "encounterType": null, "nextEncounterId": "VIS22", "previousEncounterId": "VIS20", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS22", "encounterContactModes": null, "encounterDescription": "PLANNED VISIT", "encounterEnvironmentalSetting": null, "encounterName": "FU 1", "encounterType": null, "nextEncounterId": "VIS23", "previousEncounterId": "VIS21", "transitionStartRule": null, "transitionEndRule": null}, {"Id": "VIS23", "encounterContactModes": null, "encounterDescription": "VIRTUAL VISIT", "encounterEnvironmentalSetting": null, "encounterName": "FU 2", "encounterType": null, "nextEncounterId": null, "previousEncounterId": "VIS22", "transitionStartRule": null, "transitionEndRule": null}], "studyScheduleTimelines": [{"Id": "Timeline01", "scheduleTimelineName": "name", "scheduleTimelineDescription": "Timeline for Stage III", "entryCondition": "condition", "scheduleTimelineEntryId": "INS001", "scheduleTimelineExits": [{"scheduleTimelineExitId": "Exit01"}], "scheduleTimelineInstances": [{"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"], "Id": "INS002", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS11", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 1, "scheduledInstanceTimings": [{"Id": "T01", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 1", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"Id": "T02", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "timingValue": "Day 15", "timingWindow": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"scheduledInstanceType": "DECISION", "conditionAssignments": {"option": "changes"}, "Id": "INS001", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS11", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 2, "scheduledInstanceTimings": [{"Id": "T030", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 1", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"Id": "T04", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "timingValue": "Day 15", "timingWindow": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT003", "ACT002"], "Id": "INS003", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS12", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T03", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 1", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}, {"Id": "T04", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "Before"}, "timingValue": "Day 15", "timingWindow": "window", "relativeToScheduledInstanceId": "INS002", "relativeFromScheduledInstanceId": "INS001", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "EndToEnd"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT005", "ACT006"], "Id": "INS004", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS13", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T06", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 10", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT006", "ACT007"], "Id": "INS005", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS14", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T07", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 18", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT008", "ACT009"], "Id": "INS006", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS15", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T08", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 20", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT010", "ACT011"], "Id": "INS007", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS16", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T09", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 23", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT012", "ACT013"], "Id": "INS008", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS17", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T10", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 25", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT013", "ACT014"], "Id": "INS009", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS18", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T11", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 30", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT015", "ACT016"], "Id": "INS010", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS19", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T12", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "Day 35", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT017", "ACT018"], "Id": "INS011", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS20", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T13", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "WEEK 1", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT018", "ACT019"], "Id": "INS012", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS21", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T14", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "WEEK 3", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT019", "ACT002"], "Id": "INS013", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS22", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T15", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "WEEK 5", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT003", "ACT001"], "Id": "INS014", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS23", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T16", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "WEEK 6", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}, {"scheduledInstanceType": "ACTIVITY", "activityIds": ["ACT005", "ACT002"], "Id": "INS015", "scheduleTimelineExitId": "Exit01", "scheduledInstanceEncounterId": "VIS23", "scheduledInstanceTimelineId": "Timeline01", "scheduleSequenceNumber": 3, "scheduledInstanceTimings": [{"Id": "T17", "timingType": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "After"}, "timingValue": "MONTH 1", "timingWindow": "window", "relativeToScheduledInstanceId": "INS001", "relativeFromScheduledInstanceId": "INS002", "timingRelativeToFrom": {"codeId": "END001", "code": "C9834x", "codeSystem": "http:www.cdisc.org", "codeSystemVersion": "1", "decode": "StartToStart"}}]}]}], "studyId": "c2254c40-a085-4ae1-bcdc-be61cd88760a", "studyTitle": "Study of Stomach Cancer", "studyDesigns": [{"studyDesignId": "SD01", "studyDesignName": "Design for Lung Cancer", "studyDesignDescription": "Stage III Lung Cancer", "studyScheduleTimelines": [{"scheduleTimelineId": "Timeline01", "scheduleTimelineName": "name", "scheduleTimelineDescription": "Timeline for Stage III", "entryCondition": "condition", "scheduleTimelineSoA": {"orderOfActivities": [{"activityId": "ACT001", "activityDescription": "Informed consents", "activityName": "Informed consent", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "Reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT002", "activityDescription": "Informed consent", "activityName": "Eligibility criteria", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT003", "activityDescription": "Informed consent", "activityName": "Demography", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT005", "activityDescription": "Informed consent", "activityName": "Disease characteristics", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT006", "activityDescription": "Informed consent", "activityName": "Physical exam", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT007", "activityDescription": "Informed consent", "activityName": "Height", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT008", "activityDescription": "Informed consent", "activityName": "12-lead ECG", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT009", "activityDescription": "Informed consent", "activityName": "Hematology (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT010", "activityDescription": "Informed consent", "activityName": "Chemistry (predose)", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT011", "activityDescription": "Informed consent", "activityName": "Serology", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT012", "activityDescription": "Informed consent", "activityName": "Urinalysis", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT013", "activityDescription": "Informed consent", "activityName": "Pregnancy test", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT014", "activityDescription": "Informed consent", "activityName": "Ensure availability of medication X", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT015", "activityDescription": "Informed consent", "activityName": "Hospitalization", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT016", "activityDescription": "Informed consent", "activityName": "Weight", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT017", "activityDescription": "Informed consent", "activityName": "Vital signs", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT018", "activityDescription": "Informed consent", "activityName": "adverse events", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}, {"activityId": "ACT019", "activityDescription": "Informed consent", "activityName": "Concomitant medications", "definedProcedures": [{"procedureId": "f4e2afcd-0b32-4d16-9247-bcc45604bc13", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": false, "procedureIsConditionalReason": "Reasons", "footnoteId": "", "footnoteDescription": ""}, {"procedureId": "f4e2", "procedureName": "name", "procedureDescription": "des", "procedureIsConditional": true, "procedureIsConditionalReason": "Reason", "footnoteId": "", "footnoteDescription": " : Reason"}], "activityIsConditional": false, "activityIsConditionalReason": "reason", "biomedicalConcepts": ["Diastolic Blood Pressure "], "activityTimelineId": "Timeline01", "activityTimelineName": "name", "footnoteId": "", "footnoteDescription": ""}], "SoA": [{"encounterId": "VIS11", "encounterName": "SCREENING VISIT", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT001", "ACT002", "ACT003", "ACT005", "ACT008", "ACT009", "ACT011", "ACT018"]}]}, {"encounterId": "VIS12", "encounterName": "RUN-IN VISIT 1", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT002"]}, {"timingValue": "Day 15", "timingWindow": "window", "timingType": "Before", "activities": ["ACT003", "ACT002"]}]}, {"encounterId": "VIS13", "encounterName": "RUN-IN VISIT 2", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 10", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT006"]}]}, {"encounterId": "VIS14", "encounterName": "RUN-IN VISIT 3", "encounterScheduledAtTimingValue": "Day 15", "timings": [{"timingValue": "Day 18", "timingWindow": "window", "timingType": "After", "activities": ["ACT006", "ACT007"]}]}, {"encounterId": "VIS15", "encounterName": "CYCLE 1, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 1", "timings": [{"timingValue": "Day 20", "timingWindow": "window", "timingType": "After", "activities": ["ACT008", "ACT009"]}]}, {"encounterId": "VIS16", "encounterName": "CYCLE 1, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 10", "timings": [{"timingValue": "Day 23", "timingWindow": "window", "timingType": "After", "activities": ["ACT010", "ACT011"]}]}, {"encounterId": "VIS17", "encounterName": "CYCLE 1, TREATMENT 3", "encounterScheduledAtTimingValue": "Day 18", "timings": [{"timingValue": "Day 25", "timingWindow": "window", "timingType": "After", "activities": ["ACT012", "ACT013"]}]}, {"encounterId": "VIS18", "encounterName": "CYCLE 2, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 20", "timings": [{"timingValue": "Day 30", "timingWindow": "window", "timingType": "After", "activities": ["ACT013", "ACT014"]}]}, {"encounterId": "VIS19", "encounterName": "CYCLE 2, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 23", "timings": [{"timingValue": "Day 35", "timingWindow": "window", "timingType": "After", "activities": ["ACT015", "ACT016"]}]}, {"encounterId": "VIS20", "encounterName": "CYCLE 3, TREATMENT 1", "encounterScheduledAtTimingValue": "Day 25", "timings": [{"timingValue": "WEEK 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT017", "ACT018"]}]}, {"encounterId": "VIS21", "encounterName": "CYCLE 3, TREATMENT 2", "encounterScheduledAtTimingValue": "Day 30", "timings": [{"timingValue": "WEEK 3", "timingWindow": "window", "timingType": "After", "activities": ["ACT018", "ACT019"]}]}, {"encounterId": "VIS22", "encounterName": "FU 1", "encounterScheduledAtTimingValue": "WEEK 6", "timings": [{"timingValue": "WEEK 5", "timingWindow": "window", "timingType": "After", "activities": ["ACT019", "ACT002"]}]}, {"encounterId": "VIS23", "encounterName": "FU 2", "encounterScheduledAtTimingValue": "MONTH 1", "timings": [{"timingValue": "WEEK 6", "timingWindow": "window", "timingType": "After", "activities": ["ACT003", "ACT001"]}, {"timingValue": "MONTH 1", "timingWindow": "window", "timingType": "After", "activities": ["ACT005", "ACT002"]}]}]}}]}]}