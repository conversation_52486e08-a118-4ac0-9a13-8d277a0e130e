﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
	  <GenerateDocumentationFile>true</GenerateDocumentationFile>
	  <NoWarn>$(NoWarn);1591</NoWarn>
	  <DocumentationMarkdown>$(MSBuildProjectDirectory)\TransCelerate.SDR.WebApi.md</DocumentationMarkdown>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Mappers\eCPT\**" />
    <Content Remove="Mappers\eCPT\**" />
    <EmbeddedResource Remove="Mappers\eCPT\**" />
    <None Remove="Mappers\eCPT\**" />
  </ItemGroup>

  <ItemGroup>
    <Content Remove="ConformanceRules.json" />
    <Content Remove="SdrCptMasterDataMapping.json" />
  </ItemGroup>

  <ItemGroup>
    <None Include="Data\ConformanceRules.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
    <None Include="Data\SdrCptMasterDataMapping.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper.Extensions.Microsoft.DependencyInjection" Version="12.0.1" />
    <PackageReference Include="Azure.Extensions.AspNetCore.Configuration.Secrets" Version="1.2.2" />
    <PackageReference Include="Azure.Identity" Version="1.13.2" />
    <PackageReference Include="Azure.Messaging.ServiceBus" Version="7.18.3" />
    <PackageReference Include="Azure.Security.KeyVault.Secrets" Version="4.5.0" />
    <PackageReference Include="FluentValidation.AspNetCore" Version="11.3.0" />
    <PackageReference Include="JsonSubTypes" Version="2.0.1" />
    <PackageReference Include="Microsoft.ApplicationInsights.AspNetCore" Version="2.21.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.19" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Versioning" Version="5.1.0" />
    <PackageReference Include="Microsoft.Extensions.Azure" Version="1.6.3" />
    <PackageReference Include="Microsoft.Identity.Web" Version="2.9.0" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" Version="2.9.0" />
    <PackageReference Include="Moq" Version="4.18.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="6.5.0" />
    <PackageReference Include="Swashbuckle.AspNetCore.Newtonsoft" Version="6.5.0" />
    <PackageReference Include="System.IO.FileSystem.Primitives" Version="4.3.0" />
    <PackageReference Include="System.Runtime.Handles" Version="4.3.0" />
    <PackageReference Include="System.Runtime.InteropServices" Version="4.3.0" />
    <PackageReference Include="Vsxmd" Version="1.4.6">
      <!--<PackageReference Include="MongoDB.Driver" Version="3.1.0" />-->
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\TransCelerate.SDR.Core\TransCelerate.SDR.Core.csproj" />
    <ProjectReference Include="..\TransCelerate.SDR.DataAccess\TransCelerate.SDR.DataAccess.csproj" />
    <ProjectReference Include="..\TransCelerate.SDR.RuleEngine\TransCelerate.SDR.RuleEngine.csproj" />
    <ProjectReference Include="..\TransCelerate.SDR.Service\TransCelerate.SDR.Services.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Content Update="Utilities\Swagger.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>


</Project>
