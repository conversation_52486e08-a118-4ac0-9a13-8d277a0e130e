{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "ApplicationInsights": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}}, "AzureAd": {"Audience": ""}, "AzureAd-TenantId": "", "AzureAd-ClientId": "", "AzureAd-ClientSecret": "", "AzureAd-Authority": "", "AzureAd-Audience": "", "AppInsights-ApiKey": "", "AppInsights-AppId": "", "AppInsights-RESTApiUrl": "", "ConnectionStrings": {"DefaultConnection": "", "DatabaseName": ""}, "AzureServiceBusConnectionString": "", "AzureServiceBusQueueName": "", "KeyVault": {"Vault": "{#KeyVault-Name#}", "ClientId": "", "ClientSecret": ""}, "AllowedHosts": "*", "ApplicationInsights": {"InstrumentationKey": ""}, "StudyHistory": {"DateRange": ""}, "ApiVersionUsdmVersionMapping": "", "SdrCptMasterDataMapping": "", "ConformanceRules": "", "CdiscRulesEngine": ""}